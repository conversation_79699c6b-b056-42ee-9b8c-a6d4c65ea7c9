"use client";

import Link from "next/link";
import { useRouter, useParams } from "next/navigation";
import {
  ArrowRight,
  MapPin,
  Calendar,
  User,
  Star,
  MessageCircle,
  Heart,
  Share2,
  Flag,
  Repeat,
  ChevronLeft,
  ChevronRight
} from "lucide-react";

// Mock data - in a real app, this would come from an API
const getItemData = (id: string) => {
  const items = {
    "1": {
      id: 1,
      title: "آيفون 12 مستعمل",
      description: "آيفون 12 باللون الأزرق، سعة 128 جيجا، حالة ممتازة جداً. تم استخدامه لمدة سنة واحدة فقط. يأتي مع الشاحن الأصلي والعلبة وجميع الملحقات. لا توجد خدوش أو كسور، البطارية بحالة ممتازة 89%. تم تحديث النظام لآخر إصدار.",
      wantedItem: "لابتوب أو تابلت بمواصفات جيدة، يفضل MacBook أو iPad، أو أي جهاز إلكتروني مشابه في القيمة",
      location: "الرياض - حي النخيل",
      condition: "ممتاز",
      category: "إلكترونيات",
      postedDate: "منذ 3 أيام",
      images: ["/api/placeholder/600/400", "/api/placeholder/600/400", "/api/placeholder/600/400", "/api/placeholder/600/400"],
      owner: { name: "أحمد محمد", rating: 4.8, reviewsCount: 23, joinDate: "عضو منذ 6 أشهر", avatar: "/api/placeholder/100/100", verified: true }
    },
    "2": {
      id: 2,
      title: "طاولة خشبية كلاسيكية",
      description: "طاولة طعام خشبية أنيقة لـ 6 أشخاص، مصنوعة من خشب البلوط الطبيعي. الطاولة في حالة جيدة جداً مع بعض علامات الاستخدام الطبيعي. تم صيانتها مؤخراً وتلميعها. الأبعاد: 180سم × 90سم × 75سم. مثالية للعائلات أو المكاتب.",
      wantedItem: "كنبة مريحة لـ 3 أشخاص، أو طقم كراسي، أو أي أثاث منزلي مناسب",
      location: "جدة - حي الروضة",
      condition: "جيد جداً",
      category: "أثاث",
      postedDate: "منذ 5 أيام",
      images: ["/api/placeholder/600/400", "/api/placeholder/600/400", "/api/placeholder/600/400"],
      owner: { name: "فاطمة أحمد", rating: 4.6, reviewsCount: 18, joinDate: "عضو منذ 8 أشهر", avatar: "/api/placeholder/100/100", verified: true }
    },
    "3": {
      id: 3,
      title: "دراجة هوائية رياضية",
      description: "دراجة هوائية رياضية مقاس 26 بوصة، مناسبة للكبار. تم استخدامها بشكل خفيف لمدة سنتين. الإطارات والفرامل في حالة ممتازة. تم تغيير السلسلة مؤخراً. مثالية للرياضة أو التنقل اليومي. تأتي مع خوذة وأدوات الصيانة الأساسية.",
      wantedItem: "معدات رياضية أخرى، أو جهاز مشي، أو أوزان رياضية",
      location: "الدمام - حي الشاطئ",
      condition: "جيد جداً",
      category: "رياضة",
      postedDate: "منذ أسبوع",
      images: ["/api/placeholder/600/400", "/api/placeholder/600/400"],
      owner: { name: "محمد علي", rating: 4.9, reviewsCount: 31, joinDate: "عضو منذ سنة", avatar: "/api/placeholder/100/100", verified: true }
    }
  };

  return items[id as keyof typeof items] || items["1"];
};

const relatedItems = [
  {
    id: 2,
    title: "سماعات AirPods Pro",
    wantedItem: "ساعة ذكية",
    location: "الرياض",
    condition: "جيد جداً"
  },
  {
    id: 3,
    title: "iPad Air مستعمل",
    wantedItem: "جوال أو لابتوب",
    location: "جدة",
    condition: "ممتاز"
  },
  {
    id: 4,
    title: "MacBook Pro 2020",
    wantedItem: "كاميرا احترافية",
    location: "الدمام",
    condition: "جيد"
  }
];

export default function ItemDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const mockItem = getItemData(params.id as string);

  const handleExchangeProposal = () => {
    // في التطبيق الحقيقي، ستتم إضافة المنطق هنا لإنشاء محادثة جديدة
    alert(`تم إرسال طلب مقايضة لـ ${mockItem.owner.name} بنجاح! سيتم توجيهك لصفحة الرسائل.`);
    router.push('/messages');
  };

  const handleLike = () => {
    alert('تم إضافة الغرض إلى المفضلة!');
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: mockItem.title,
        text: `شاهد هذا الغرض للمقايضة: ${mockItem.title}`,
        url: window.location.href,
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
      alert('تم نسخ رابط الغرض!');
    }
  };

  const handleReport = () => {
    const reason = prompt('سبب الإبلاغ:');
    if (reason) {
      alert('تم إرسال البلاغ بنجاح. سيتم مراجعته من قبل فريقنا.');
    }
  };
  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-blue-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-md border-b border-emerald-100 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="flex items-center space-x-2">
              <div className="bg-gradient-to-r from-emerald-500 to-blue-500 p-2 rounded-xl">
                <Repeat className="h-6 w-6 text-white" />
              </div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-emerald-600 to-blue-600 bg-clip-text text-transparent">
                مقايضة
              </h1>
            </Link>

            <nav className="hidden md:flex items-center space-x-8">
              <Link href="/browse" className="text-gray-700 hover:text-emerald-600 transition-colors">
                تصفح الأغراض
              </Link>
              <Link href="/add-item" className="text-gray-700 hover:text-emerald-600 transition-colors">
                أضف غرض
              </Link>
              <Link href="/messages" className="text-gray-700 hover:text-emerald-600 transition-colors">
                الرسائل
              </Link>
            </nav>

            <div className="flex items-center space-x-4">
              <Link
                href="/profile"
                className="text-gray-700 hover:text-emerald-600 transition-colors"
              >
                حسابي
              </Link>
              <Link
                href="/add-item"
                className="bg-gradient-to-r from-emerald-500 to-blue-500 text-white px-6 py-2 rounded-full hover:shadow-lg transition-all duration-300 transform hover:scale-105"
              >
                أضف غرض
              </Link>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <div className="flex items-center text-sm text-gray-600 mb-6">
          <Link href="/" className="hover:text-emerald-600">الرئيسية</Link>
          <ChevronLeft className="h-4 w-4 mx-2" />
          <Link href="/browse" className="hover:text-emerald-600">تصفح الأغراض</Link>
          <ChevronLeft className="h-4 w-4 mx-2" />
          <span className="text-gray-900">{mockItem.title}</span>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {/* Image Gallery */}
            <div className="bg-white/60 backdrop-blur-sm rounded-2xl border border-emerald-100 p-6 mb-6">
              <div className="relative">
                <div className="aspect-video bg-gradient-to-br from-emerald-100 to-blue-100 rounded-xl mb-4 flex items-center justify-center">
                  <span className="text-gray-500 text-lg">صورة رئيسية للغرض</span>
                </div>

                {/* Thumbnail Gallery */}
                <div className="grid grid-cols-4 gap-2">
                  {mockItem.images.map((_, index) => (
                    <div key={index} className="aspect-square bg-gradient-to-br from-emerald-100 to-blue-100 rounded-lg flex items-center justify-center cursor-pointer hover:opacity-80 transition-opacity">
                      <span className="text-xs text-gray-500">{index + 1}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Item Details */}
            <div className="bg-white/60 backdrop-blur-sm rounded-2xl border border-emerald-100 p-6 mb-6">
              <div className="flex items-start justify-between mb-4">
                <div>
                  <span className="inline-block bg-emerald-100 text-emerald-800 px-3 py-1 rounded-full text-sm font-medium mb-2">
                    {mockItem.category}
                  </span>
                  <h1 className="text-3xl font-bold text-gray-900 mb-2">{mockItem.title}</h1>
                  <div className="flex items-center text-gray-600 text-sm space-x-4">
                    <div className="flex items-center">
                      <MapPin className="h-4 w-4 ml-1" />
                      {mockItem.location}
                    </div>
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 ml-1" />
                      {mockItem.postedDate}
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <button
                    onClick={handleLike}
                    className="p-2 bg-gray-100 rounded-full hover:bg-red-100 hover:text-red-600 transition-colors"
                    title="إضافة للمفضلة"
                  >
                    <Heart className="h-5 w-5 text-gray-600 hover:text-red-600" />
                  </button>
                  <button
                    onClick={handleShare}
                    className="p-2 bg-gray-100 rounded-full hover:bg-blue-100 hover:text-blue-600 transition-colors"
                    title="مشاركة الغرض"
                  >
                    <Share2 className="h-5 w-5 text-gray-600 hover:text-blue-600" />
                  </button>
                  <button
                    onClick={handleReport}
                    className="p-2 bg-gray-100 rounded-full hover:bg-yellow-100 hover:text-yellow-600 transition-colors"
                    title="إبلاغ عن الغرض"
                  >
                    <Flag className="h-5 w-5 text-gray-600 hover:text-yellow-600" />
                  </button>
                </div>
              </div>

              <div className="mb-6">
                <span className="inline-block bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                  الحالة: {mockItem.condition}
                </span>
              </div>

              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">وصف الغرض</h3>
                <p className="text-gray-700 leading-relaxed">{mockItem.description}</p>
              </div>

              <div className="bg-blue-50 p-4 rounded-xl">
                <h3 className="text-lg font-semibold text-blue-900 mb-2">يريد في المقابل:</h3>
                <p className="text-blue-800">{mockItem.wantedItem}</p>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            {/* Owner Info */}
            <div className="bg-white/60 backdrop-blur-sm rounded-2xl border border-emerald-100 p-6 mb-6">
              <div className="flex items-center mb-4">
                <div className="w-16 h-16 bg-gradient-to-br from-emerald-100 to-blue-100 rounded-full flex items-center justify-center ml-4">
                  <User className="h-8 w-8 text-gray-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 flex items-center">
                    {mockItem.owner.name}
                    {mockItem.owner.verified && (
                      <div className="w-5 h-5 bg-emerald-500 rounded-full flex items-center justify-center mr-2">
                        <span className="text-white text-xs">✓</span>
                      </div>
                    )}
                  </h3>
                  <div className="flex items-center text-sm text-gray-600">
                    <Star className="h-4 w-4 text-yellow-400 ml-1" />
                    {mockItem.owner.rating} ({mockItem.owner.reviewsCount} تقييم)
                  </div>
                  <p className="text-sm text-gray-500">{mockItem.owner.joinDate}</p>
                </div>
              </div>

              <div className="space-y-3">
                <button
                  onClick={handleExchangeProposal}
                  className="w-full bg-gradient-to-r from-emerald-500 to-blue-500 text-white py-3 rounded-xl font-semibold hover:shadow-lg transition-all duration-300 transform hover:scale-105 flex items-center justify-center"
                >
                  <MessageCircle className="h-5 w-5 ml-2" />
                  اقترح تبادل
                </button>

                <Link
                  href={`/profile/${encodeURIComponent(mockItem.owner.name)}`}
                  className="w-full border-2 border-emerald-200 text-emerald-600 py-3 rounded-xl font-semibold hover:bg-emerald-50 transition-colors text-center block"
                >
                  عرض الملف الشخصي
                </Link>
              </div>
            </div>

            {/* Safety Tips */}
            <div className="bg-yellow-50 border border-yellow-200 rounded-2xl p-6 mb-6">
              <h3 className="font-semibold text-yellow-800 mb-3">نصائح للأمان</h3>
              <ul className="text-sm text-yellow-700 space-y-2">
                <li>• التق في مكان عام وآمن</li>
                <li>• تأكد من حالة الغرض قبل التبادل</li>
                <li>• لا تشارك معلومات شخصية حساسة</li>
                <li>• استخدم نظام الرسائل في المنصة</li>
              </ul>
            </div>

            {/* Related Items */}
            <div className="bg-white/60 backdrop-blur-sm rounded-2xl border border-emerald-100 p-6">
              <h3 className="font-semibold text-gray-900 mb-4">أغراض مشابهة</h3>
              <div className="space-y-4">
                {relatedItems.map((item) => (
                  <Link
                    key={item.id}
                    href={`/items/${item.id}`}
                    className="block p-3 border border-emerald-200 rounded-xl hover:bg-emerald-50 transition-colors"
                  >
                    <h4 className="font-medium text-gray-900 text-sm mb-1">{item.title}</h4>
                    <p className="text-xs text-gray-600 mb-2">يريد: {item.wantedItem}</p>
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>{item.location}</span>
                      <span className="bg-emerald-100 text-emerald-700 px-2 py-1 rounded">{item.condition}</span>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}

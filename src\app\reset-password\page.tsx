"use client";

import Link from "next/link";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { 
  Repeat, 
  Lock, 
  Eye, 
  EyeOff, 
  CheckCircle,
  AlertCircle,
  Loader2,
  Shield
} from "lucide-react";

export default function ResetPasswordPage() {
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const router = useRouter();

  const validatePassword = (password: string) => {
    const errors: string[] = [];
    if (password.length < 8) errors.push("يجب أن تكون كلمة المرور 8 أحرف على الأقل");
    if (!/[A-Z]/.test(password)) errors.push("يجب أن تحتوي على حرف كبير واحد على الأقل");
    if (!/[a-z]/.test(password)) errors.push("يجب أن تحتوي على حرف صغير واحد على الأقل");
    if (!/[0-9]/.test(password)) errors.push("يجب أن تحتوي على رقم واحد على الأقل");
    return errors;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setErrors({});
    
    const newErrors: {[key: string]: string} = {};

    if (!password) {
      newErrors.password = "يرجى إدخال كلمة المرور الجديدة";
    } else {
      const passwordErrors = validatePassword(password);
      if (passwordErrors.length > 0) {
        newErrors.password = passwordErrors[0];
      }
    }

    if (!confirmPassword) {
      newErrors.confirmPassword = "يرجى تأكيد كلمة المرور";
    } else if (password !== confirmPassword) {
      newErrors.confirmPassword = "كلمات المرور غير متطابقة";
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    setIsLoading(true);
    
    // محاكاة إعادة تعيين كلمة المرور
    setTimeout(() => {
      setIsLoading(false);
      setIsSuccess(true);
      
      // توجيه تلقائي لصفحة تسجيل الدخول بعد 3 ثوان
      setTimeout(() => {
        router.push('/login');
      }, 3000);
    }, 2000);
  };

  if (isSuccess) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-blue-50 flex items-center justify-center p-4">
        <div className="max-w-md w-full">
          <div className="bg-white/60 backdrop-blur-sm rounded-2xl border border-emerald-100 p-8 text-center">
            <div className="bg-green-100 p-4 rounded-full w-fit mx-auto mb-6">
              <CheckCircle className="h-12 w-12 text-green-600" />
            </div>
            
            <h2 className="text-2xl font-bold text-gray-900 mb-4">تم تغيير كلمة المرور!</h2>
            
            <p className="text-gray-600 mb-6 leading-relaxed">
              تم تغيير كلمة المرور بنجاح. يمكنك الآن تسجيل الدخول باستخدام كلمة المرور الجديدة.
            </p>

            <div className="bg-blue-50 border border-blue-200 rounded-xl p-4 mb-6">
              <p className="text-sm text-blue-800">
                <Loader2 className="h-4 w-4 inline ml-1 animate-spin" />
                سيتم توجيهك لصفحة تسجيل الدخول خلال 3 ثوان...
              </p>
            </div>

            <Link
              href="/login"
              className="w-full bg-gradient-to-r from-emerald-500 to-blue-500 text-white py-3 rounded-xl font-semibold hover:shadow-lg transition-all duration-300 transform hover:scale-105 inline-block"
            >
              تسجيل الدخول الآن
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-blue-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full">
        {/* Header */}
        <div className="text-center mb-8">
          <Link href="/" className="inline-flex items-center space-x-2 mb-6">
            <div className="bg-gradient-to-r from-emerald-500 to-blue-500 p-3 rounded-xl">
              <Repeat className="h-8 w-8 text-white" />
            </div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-emerald-600 to-blue-600 bg-clip-text text-transparent">
              مقايضة
            </h1>
          </Link>
          
          <h2 className="text-2xl font-bold text-gray-900 mb-2">إعادة تعيين كلمة المرور</h2>
          <p className="text-gray-600">
            أدخل كلمة المرور الجديدة لحسابك
          </p>
        </div>

        {/* Form */}
        <div className="bg-white/60 backdrop-blur-sm rounded-2xl border border-emerald-100 p-8">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* New Password */}
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                كلمة المرور الجديدة
              </label>
              <div className="relative">
                <Lock className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                <input
                  type={showPassword ? "text" : "password"}
                  id="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full pr-10 pl-12 py-3 border border-emerald-200 rounded-xl focus:border-emerald-500 focus:outline-none transition-colors"
                  placeholder="أدخل كلمة المرور الجديدة"
                  disabled={isLoading}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                </button>
              </div>
              {errors.password && (
                <p className="mt-2 text-sm text-red-600 flex items-center">
                  <AlertCircle className="h-4 w-4 ml-1" />
                  {errors.password}
                </p>
              )}
            </div>

            {/* Confirm Password */}
            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-2">
                تأكيد كلمة المرور
              </label>
              <div className="relative">
                <Lock className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                <input
                  type={showConfirmPassword ? "text" : "password"}
                  id="confirmPassword"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  className="w-full pr-10 pl-12 py-3 border border-emerald-200 rounded-xl focus:border-emerald-500 focus:outline-none transition-colors"
                  placeholder="أعد إدخال كلمة المرور"
                  disabled={isLoading}
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  {showConfirmPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                </button>
              </div>
              {errors.confirmPassword && (
                <p className="mt-2 text-sm text-red-600 flex items-center">
                  <AlertCircle className="h-4 w-4 ml-1" />
                  {errors.confirmPassword}
                </p>
              )}
            </div>

            {/* Password Requirements */}
            <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
              <h3 className="font-semibold text-blue-900 mb-2 flex items-center">
                <Shield className="h-4 w-4 ml-1" />
                متطلبات كلمة المرور:
              </h3>
              <ul className="text-sm text-blue-800 space-y-1">
                <li className={password.length >= 8 ? "text-green-600" : ""}>
                  • 8 أحرف على الأقل
                </li>
                <li className={/[A-Z]/.test(password) ? "text-green-600" : ""}>
                  • حرف كبير واحد على الأقل
                </li>
                <li className={/[a-z]/.test(password) ? "text-green-600" : ""}>
                  • حرف صغير واحد على الأقل
                </li>
                <li className={/[0-9]/.test(password) ? "text-green-600" : ""}>
                  • رقم واحد على الأقل
                </li>
              </ul>
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-gradient-to-r from-emerald-500 to-blue-500 text-white py-3 rounded-xl font-semibold hover:shadow-lg transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center"
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-5 w-5 animate-spin ml-2" />
                  جاري التحديث...
                </>
              ) : (
                "تحديث كلمة المرور"
              )}
            </button>
          </form>
        </div>

        {/* Security Note */}
        <div className="mt-6 bg-yellow-50 border border-yellow-200 rounded-2xl p-6">
          <h3 className="font-semibold text-yellow-800 mb-3">نصائح الأمان:</h3>
          <div className="space-y-2 text-sm text-yellow-700">
            <p>• استخدم كلمة مرور قوية وفريدة</p>
            <p>• لا تشارك كلمة المرور مع أي شخص</p>
            <p>• قم بتغيير كلمة المرور بانتظام</p>
            <p>• استخدم كلمات مرور مختلفة لكل موقع</p>
          </div>
        </div>

        {/* Back to Login */}
        <div className="mt-4 text-center">
          <Link
            href="/login"
            className="text-emerald-600 hover:text-emerald-700 font-medium transition-colors"
          >
            العودة لتسجيل الدخول
          </Link>
        </div>
      </div>
    </div>
  );
}

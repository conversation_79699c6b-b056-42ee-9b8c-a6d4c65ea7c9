@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');
@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: 'Cairo', system-ui, sans-serif;
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

html {
  font-family: 'Cairo', system-ui, sans-serif;
  direction: rtl;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Cairo', system-ui, sans-serif;
  direction: rtl;
}

/* Custom utility classes */
.btn-primary {
  @apply bg-gradient-to-r from-emerald-500 to-blue-500 text-white px-6 py-3 rounded-xl font-semibold hover:shadow-lg transition-all duration-300 transform hover:scale-105;
}

.btn-secondary {
  @apply border-2 border-emerald-200 text-emerald-600 px-6 py-3 rounded-xl font-semibold hover:bg-emerald-50 transition-colors;
}

.card {
  @apply bg-white/60 backdrop-blur-sm rounded-2xl border border-emerald-100 p-6;
}

.input-field {
  @apply w-full px-4 py-3 border-2 border-emerald-200 rounded-xl focus:border-emerald-500 focus:outline-none transition-colors;
}

.text-gradient {
  @apply bg-gradient-to-r from-emerald-600 to-blue-600 bg-clip-text text-transparent;
}

.glass-effect {
  @apply bg-white/60 backdrop-blur-sm border border-emerald-100;
}

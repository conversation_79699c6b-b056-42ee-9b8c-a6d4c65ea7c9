import Link from "next/link";
import { 
  Repeat, 
  Shield, 
  Eye, 
  Lock, 
  Database, 
  Users, 
  Mail, 
  Phone,
  Calendar,
  FileText,
  AlertTriangle,
  CheckCircle
} from "lucide-react";

export default function PrivacyPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-blue-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-md border-b border-emerald-100 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="flex items-center space-x-2">
              <div className="bg-gradient-to-r from-emerald-500 to-blue-500 p-2 rounded-xl">
                <Repeat className="h-6 w-6 text-white" />
              </div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-emerald-600 to-blue-600 bg-clip-text text-transparent">
                مقايضة
              </h1>
            </Link>
            
            <nav className="hidden md:flex items-center space-x-8">
              <Link href="/browse" className="text-gray-700 hover:text-emerald-600 transition-colors">
                تصفح الأغراض
              </Link>
              <Link href="/add-item" className="text-gray-700 hover:text-emerald-600 transition-colors">
                أضف غرض
              </Link>
              <Link href="/about" className="text-gray-700 hover:text-emerald-600 transition-colors">
                من نحن
              </Link>
              <Link href="/contact" className="text-gray-700 hover:text-emerald-600 transition-colors">
                اتصل بنا
              </Link>
            </nav>

            <div className="flex items-center space-x-4">
              <Link href="/login" className="text-gray-700 hover:text-emerald-600 transition-colors">
                تسجيل الدخول
              </Link>
              <Link href="/register" className="bg-gradient-to-r from-emerald-500 to-blue-500 text-white px-6 py-2 rounded-full hover:shadow-lg transition-all duration-300 transform hover:scale-105">
                إنشاء حساب
              </Link>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Page Header */}
        <div className="text-center mb-12">
          <div className="bg-emerald-100 p-4 rounded-full w-fit mx-auto mb-6">
            <Shield className="h-12 w-12 text-emerald-600" />
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">سياسة الخصوصية</h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            نحن ملتزمون بحماية خصوصيتك وأمان بياناتك الشخصية
          </p>
          <div className="flex items-center justify-center mt-4 text-sm text-gray-500">
            <Calendar className="h-4 w-4 ml-1" />
            آخر تحديث: 1 يناير 2025
          </div>
        </div>

        {/* Content */}
        <div className="bg-white/60 backdrop-blur-sm rounded-2xl border border-emerald-100 p-8 space-y-8">
          
          {/* Introduction */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center">
              <FileText className="h-6 w-6 ml-2 text-emerald-600" />
              مقدمة
            </h2>
            <p className="text-gray-700 leading-relaxed">
              مرحباً بك في منصة "مقايضة". نحن نقدر ثقتك بنا ونلتزم بحماية خصوصيتك وأمان معلوماتك الشخصية. 
              توضح هذه السياسة كيفية جمعنا واستخدامنا وحمايتنا لبياناتك عند استخدام منصتنا.
            </p>
          </section>

          {/* Data Collection */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center">
              <Database className="h-6 w-6 ml-2 text-emerald-600" />
              البيانات التي نجمعها
            </h2>
            <div className="space-y-4">
              <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
                <h3 className="font-semibold text-blue-900 mb-2">المعلومات الشخصية:</h3>
                <ul className="text-blue-800 text-sm space-y-1">
                  <li>• الاسم الكامل</li>
                  <li>• عنوان البريد الإلكتروني</li>
                  <li>• رقم الهاتف</li>
                  <li>• الموقع الجغرافي (المدينة)</li>
                  <li>• صورة الملف الشخصي (اختيارية)</li>
                </ul>
              </div>
              
              <div className="bg-green-50 border border-green-200 rounded-xl p-4">
                <h3 className="font-semibold text-green-900 mb-2">معلومات الاستخدام:</h3>
                <ul className="text-green-800 text-sm space-y-1">
                  <li>• الأغراض المنشورة والمطلوبة</li>
                  <li>• الرسائل والمحادثات</li>
                  <li>• سجل النشاط والتفاعلات</li>
                  <li>• التقييمات والمراجعات</li>
                </ul>
              </div>

              <div className="bg-purple-50 border border-purple-200 rounded-xl p-4">
                <h3 className="font-semibold text-purple-900 mb-2">البيانات التقنية:</h3>
                <ul className="text-purple-800 text-sm space-y-1">
                  <li>• عنوان IP</li>
                  <li>• نوع المتصفح والجهاز</li>
                  <li>• ملفات تعريف الارتباط (Cookies)</li>
                  <li>• سجلات الوصول والاستخدام</li>
                </ul>
              </div>
            </div>
          </section>

          {/* Data Usage */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center">
              <Eye className="h-6 w-6 ml-2 text-emerald-600" />
              كيف نستخدم بياناتك
            </h2>
            <div className="grid md:grid-cols-2 gap-4">
              <div className="bg-emerald-50 border border-emerald-200 rounded-xl p-4">
                <h3 className="font-semibold text-emerald-900 mb-3">الأغراض الأساسية:</h3>
                <ul className="text-emerald-800 text-sm space-y-2">
                  <li className="flex items-start">
                    <CheckCircle className="h-4 w-4 ml-1 mt-0.5 text-emerald-600" />
                    إنشاء وإدارة حسابك
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-4 w-4 ml-1 mt-0.5 text-emerald-600" />
                    تسهيل عمليات المقايضة
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-4 w-4 ml-1 mt-0.5 text-emerald-600" />
                    التواصل بين المستخدمين
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-4 w-4 ml-1 mt-0.5 text-emerald-600" />
                    عرض الأغراض والبحث
                  </li>
                </ul>
              </div>

              <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-4">
                <h3 className="font-semibold text-yellow-900 mb-3">التحسين والأمان:</h3>
                <ul className="text-yellow-800 text-sm space-y-2">
                  <li className="flex items-start">
                    <CheckCircle className="h-4 w-4 ml-1 mt-0.5 text-yellow-600" />
                    تحسين تجربة المستخدم
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-4 w-4 ml-1 mt-0.5 text-yellow-600" />
                    منع الاحتيال والإساءة
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-4 w-4 ml-1 mt-0.5 text-yellow-600" />
                    تحليل الاستخدام والإحصائيات
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-4 w-4 ml-1 mt-0.5 text-yellow-600" />
                    إرسال الإشعارات المهمة
                  </li>
                </ul>
              </div>
            </div>
          </section>

          {/* Data Protection */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center">
              <Lock className="h-6 w-6 ml-2 text-emerald-600" />
              حماية البيانات
            </h2>
            <div className="space-y-4">
              <p className="text-gray-700 leading-relaxed">
                نتخذ إجراءات أمنية صارمة لحماية بياناتك الشخصية من الوصول غير المصرح به أو التعديل أو الكشف أو التدمير.
              </p>
              
              <div className="grid md:grid-cols-3 gap-4">
                <div className="bg-red-50 border border-red-200 rounded-xl p-4 text-center">
                  <Lock className="h-8 w-8 text-red-600 mx-auto mb-2" />
                  <h3 className="font-semibold text-red-900 mb-1">التشفير</h3>
                  <p className="text-red-800 text-sm">جميع البيانات مشفرة أثناء النقل والتخزين</p>
                </div>
                
                <div className="bg-blue-50 border border-blue-200 rounded-xl p-4 text-center">
                  <Shield className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                  <h3 className="font-semibold text-blue-900 mb-1">الحماية</h3>
                  <p className="text-blue-800 text-sm">خوادم آمنة مع حماية متقدمة من الهجمات</p>
                </div>
                
                <div className="bg-green-50 border border-green-200 rounded-xl p-4 text-center">
                  <Users className="h-8 w-8 text-green-600 mx-auto mb-2" />
                  <h3 className="font-semibold text-green-900 mb-1">الوصول المحدود</h3>
                  <p className="text-green-800 text-sm">وصول محدود للموظفين المصرح لهم فقط</p>
                </div>
              </div>
            </div>
          </section>

          {/* Data Sharing */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center">
              <Users className="h-6 w-6 ml-2 text-emerald-600" />
              مشاركة البيانات
            </h2>
            <div className="bg-red-50 border border-red-200 rounded-xl p-4 mb-4">
              <div className="flex items-start">
                <AlertTriangle className="h-5 w-5 text-red-600 ml-2 mt-0.5" />
                <div>
                  <h3 className="font-semibold text-red-900 mb-2">التزامنا:</h3>
                  <p className="text-red-800 text-sm">
                    نحن لا نبيع أو نؤجر أو نشارك معلوماتك الشخصية مع أطراف ثالثة لأغراض تجارية.
                  </p>
                </div>
              </div>
            </div>
            
            <p className="text-gray-700 leading-relaxed mb-4">
              قد نشارك معلوماتك في الحالات التالية فقط:
            </p>
            
            <ul className="space-y-2 text-gray-700">
              <li className="flex items-start">
                <CheckCircle className="h-4 w-4 ml-1 mt-1 text-emerald-600" />
                مع موافقتك الصريحة
              </li>
              <li className="flex items-start">
                <CheckCircle className="h-4 w-4 ml-1 mt-1 text-emerald-600" />
                لتقديم الخدمات المطلوبة (مثل معالجة الدفع)
              </li>
              <li className="flex items-start">
                <CheckCircle className="h-4 w-4 ml-1 mt-1 text-emerald-600" />
                للامتثال للقوانين والأنظمة
              </li>
              <li className="flex items-start">
                <CheckCircle className="h-4 w-4 ml-1 mt-1 text-emerald-600" />
                لحماية حقوقنا وحقوق المستخدمين الآخرين
              </li>
            </ul>
          </section>

          {/* User Rights */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center">
              <Shield className="h-6 w-6 ml-2 text-emerald-600" />
              حقوقك
            </h2>
            <div className="grid md:grid-cols-2 gap-4">
              <div className="space-y-3">
                <div className="bg-emerald-50 border border-emerald-200 rounded-lg p-3">
                  <h3 className="font-semibold text-emerald-900 mb-1">الوصول</h3>
                  <p className="text-emerald-800 text-sm">طلب نسخة من بياناتك الشخصية</p>
                </div>
                
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                  <h3 className="font-semibold text-blue-900 mb-1">التصحيح</h3>
                  <p className="text-blue-800 text-sm">تحديث أو تصحيح معلوماتك</p>
                </div>
              </div>
              
              <div className="space-y-3">
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                  <h3 className="font-semibold text-yellow-900 mb-1">الحذف</h3>
                  <p className="text-yellow-800 text-sm">طلب حذف حسابك وبياناتك</p>
                </div>
                
                <div className="bg-purple-50 border border-purple-200 rounded-lg p-3">
                  <h3 className="font-semibold text-purple-900 mb-1">النقل</h3>
                  <p className="text-purple-800 text-sm">الحصول على بياناتك بصيغة قابلة للنقل</p>
                </div>
              </div>
            </div>
          </section>

          {/* Cookies */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">ملفات تعريف الارتباط (Cookies)</h2>
            <p className="text-gray-700 leading-relaxed mb-4">
              نستخدم ملفات تعريف الارتباط لتحسين تجربتك وتذكر تفضيلاتك. يمكنك التحكم في هذه الملفات من خلال إعدادات متصفحك.
            </p>
            
            <div className="bg-gray-50 border border-gray-200 rounded-xl p-4">
              <h3 className="font-semibold text-gray-900 mb-2">أنواع ملفات تعريف الارتباط:</h3>
              <ul className="text-gray-700 text-sm space-y-1">
                <li>• <strong>الضرورية:</strong> مطلوبة لعمل الموقع</li>
                <li>• <strong>الوظيفية:</strong> لتذكر تفضيلاتك</li>
                <li>• <strong>التحليلية:</strong> لفهم كيفية استخدام الموقع</li>
                <li>• <strong>التسويقية:</strong> لعرض محتوى مخصص (بموافقتك)</li>
              </ul>
            </div>
          </section>

          {/* Contact */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center">
              <Mail className="h-6 w-6 ml-2 text-emerald-600" />
              تواصل معنا
            </h2>
            <p className="text-gray-700 leading-relaxed mb-4">
              إذا كان لديك أي أسئلة حول سياسة الخصوصية أو ترغب في ممارسة حقوقك، يرجى التواصل معنا:
            </p>
            
            <div className="grid md:grid-cols-2 gap-4">
              <div className="bg-emerald-50 border border-emerald-200 rounded-xl p-4">
                <div className="flex items-center mb-2">
                  <Mail className="h-5 w-5 text-emerald-600 ml-2" />
                  <span className="font-semibold text-emerald-900">البريد الإلكتروني</span>
                </div>
                <p className="text-emerald-800"><EMAIL></p>
              </div>
              
              <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
                <div className="flex items-center mb-2">
                  <Phone className="h-5 w-5 text-blue-600 ml-2" />
                  <span className="font-semibold text-blue-900">الهاتف</span>
                </div>
                <p className="text-blue-800">+966 50 123 4567</p>
              </div>
            </div>
          </section>

          {/* Updates */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">تحديثات السياسة</h2>
            <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-4">
              <p className="text-yellow-800 text-sm leading-relaxed">
                قد نقوم بتحديث هذه السياسة من وقت لآخر. سنقوم بإشعارك بأي تغييرات مهمة عبر البريد الإلكتروني أو من خلال إشعار على المنصة. 
                ننصحك بمراجعة هذه الصفحة بانتظام للاطلاع على أحدث المعلومات.
              </p>
            </div>
          </section>
        </div>

        {/* Back to Home */}
        <div className="text-center mt-8">
          <Link 
            href="/" 
            className="inline-flex items-center text-emerald-600 hover:text-emerald-700 font-medium transition-colors"
          >
            ← العودة للصفحة الرئيسية
          </Link>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 text-white mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="flex items-center justify-center space-x-2 mb-4">
              <div className="bg-gradient-to-r from-emerald-500 to-blue-500 p-2 rounded-xl">
                <Repeat className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-bold">مقايضة</h3>
            </div>
            <p className="text-gray-400 text-sm mb-6">
              © 2025 جميع الحقوق محفوظة لـ <span className="text-emerald-400 font-semibold">مقايضة</span>
            </p>
            <div className="text-xs text-gray-500">
              مطور بـ ❤️ في السعودية
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}

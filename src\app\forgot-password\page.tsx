"use client";

import Link from "next/link";
import { useState } from "react";
import { 
  Repeat, 
  Mail, 
  ArrowRight, 
  CheckCircle,
  AlertCircle,
  Loader2
} from "lucide-react";

export default function ForgotPasswordPage() {
  const [email, setEmail] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    
    if (!email) {
      setError("يرجى إدخال البريد الإلكتروني");
      return;
    }

    if (!email.includes("@")) {
      setError("يرجى إدخال بريد إلكتروني صحيح");
      return;
    }

    setIsLoading(true);
    
    // محاكاة إرسال البريد الإلكتروني
    setTimeout(() => {
      setIsLoading(false);
      setIsSubmitted(true);
    }, 2000);
  };

  if (isSubmitted) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-blue-50 flex items-center justify-center p-4">
        <div className="max-w-md w-full">
          {/* Header */}
          <div className="text-center mb-8">
            <Link href="/" className="inline-flex items-center space-x-2 mb-6">
              <div className="bg-gradient-to-r from-emerald-500 to-blue-500 p-3 rounded-xl">
                <Repeat className="h-8 w-8 text-white" />
              </div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-emerald-600 to-blue-600 bg-clip-text text-transparent">
                مقايضة
              </h1>
            </Link>
          </div>

          {/* Success Message */}
          <div className="bg-white/60 backdrop-blur-sm rounded-2xl border border-emerald-100 p-8 text-center">
            <div className="bg-green-100 p-4 rounded-full w-fit mx-auto mb-6">
              <CheckCircle className="h-12 w-12 text-green-600" />
            </div>
            
            <h2 className="text-2xl font-bold text-gray-900 mb-4">تم إرسال الرابط!</h2>
            
            <p className="text-gray-600 mb-6 leading-relaxed">
              تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني:
              <br />
              <span className="font-semibold text-emerald-600">{email}</span>
            </p>

            <div className="bg-blue-50 border border-blue-200 rounded-xl p-4 mb-6">
              <h3 className="font-semibold text-blue-900 mb-2">الخطوات التالية:</h3>
              <ul className="text-sm text-blue-800 space-y-1 text-right">
                <li>• تحقق من صندوق الوارد في بريدك الإلكتروني</li>
                <li>• ابحث عن رسالة من "مقايضة"</li>
                <li>• اضغط على الرابط في الرسالة</li>
                <li>• أدخل كلمة المرور الجديدة</li>
              </ul>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-4 mb-6">
              <p className="text-sm text-yellow-800">
                <AlertCircle className="h-4 w-4 inline ml-1" />
                لم تجد الرسالة؟ تحقق من مجلد الرسائل غير المرغوب فيها (Spam)
              </p>
            </div>

            <div className="space-y-3">
              <button
                onClick={() => {
                  setIsSubmitted(false);
                  setEmail("");
                }}
                className="w-full bg-gradient-to-r from-emerald-500 to-blue-500 text-white py-3 rounded-xl font-semibold hover:shadow-lg transition-all duration-300 transform hover:scale-105"
              >
                إرسال رابط جديد
              </button>
              
              <Link
                href="/login"
                className="w-full border-2 border-emerald-200 text-emerald-600 py-3 rounded-xl font-semibold hover:bg-emerald-50 transition-colors text-center block"
              >
                العودة لتسجيل الدخول
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-blue-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full">
        {/* Header */}
        <div className="text-center mb-8">
          <Link href="/" className="inline-flex items-center space-x-2 mb-6">
            <div className="bg-gradient-to-r from-emerald-500 to-blue-500 p-3 rounded-xl">
              <Repeat className="h-8 w-8 text-white" />
            </div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-emerald-600 to-blue-600 bg-clip-text text-transparent">
              مقايضة
            </h1>
          </Link>
          
          <h2 className="text-2xl font-bold text-gray-900 mb-2">نسيت كلمة المرور؟</h2>
          <p className="text-gray-600">
            لا تقلق! أدخل بريدك الإلكتروني وسنرسل لك رابط لإعادة تعيين كلمة المرور
          </p>
        </div>

        {/* Form */}
        <div className="bg-white/60 backdrop-blur-sm rounded-2xl border border-emerald-100 p-8">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                البريد الإلكتروني
              </label>
              <div className="relative">
                <Mail className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                <input
                  type="email"
                  id="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full pr-10 pl-4 py-3 border border-emerald-200 rounded-xl focus:border-emerald-500 focus:outline-none transition-colors"
                  placeholder="أدخل بريدك الإلكتروني"
                  disabled={isLoading}
                />
              </div>
              {error && (
                <p className="mt-2 text-sm text-red-600 flex items-center">
                  <AlertCircle className="h-4 w-4 ml-1" />
                  {error}
                </p>
              )}
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-gradient-to-r from-emerald-500 to-blue-500 text-white py-3 rounded-xl font-semibold hover:shadow-lg transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center"
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-5 w-5 animate-spin ml-2" />
                  جاري الإرسال...
                </>
              ) : (
                "إرسال رابط إعادة التعيين"
              )}
            </button>
          </form>

          {/* Back to Login */}
          <div className="mt-6 text-center">
            <Link
              href="/login"
              className="text-emerald-600 hover:text-emerald-700 font-medium inline-flex items-center transition-colors"
            >
              <ArrowRight className="h-4 w-4 ml-1 rotate-180" />
              العودة لتسجيل الدخول
            </Link>
          </div>
        </div>

        {/* Help Section */}
        <div className="mt-6 bg-blue-50 border border-blue-200 rounded-2xl p-6">
          <h3 className="font-semibold text-blue-900 mb-3">تحتاج مساعدة؟</h3>
          <div className="space-y-2 text-sm text-blue-800">
            <p>• تأكد من إدخال البريد الإلكتروني المسجل في حسابك</p>
            <p>• قد يستغرق وصول الرسالة بضع دقائق</p>
            <p>• تحقق من مجلد الرسائل غير المرغوب فيها</p>
          </div>
          
          <div className="mt-4 pt-4 border-t border-blue-200">
            <p className="text-sm text-blue-700">
              لا تزال تواجه مشكلة؟ 
              <Link href="/contact" className="font-semibold hover:underline mr-1">
                تواصل معنا
              </Link>
            </p>
          </div>
        </div>

        {/* Security Note */}
        <div className="mt-4 text-center">
          <p className="text-xs text-gray-500">
            🔒 جميع البيانات محمية ومشفرة لضمان أمانك
          </p>
        </div>
      </div>
    </div>
  );
}

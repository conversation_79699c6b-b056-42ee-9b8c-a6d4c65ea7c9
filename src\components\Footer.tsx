import Link from "next/link";
import { Repeat } from "lucide-react";

interface FooterProps {
  variant?: 'full' | 'simple';
}

export default function Footer({ variant = 'simple' }: FooterProps) {
  if (variant === 'full') {
    return (
      <footer className="bg-gray-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid md:grid-cols-4 gap-8">
            {/* Brand */}
            <div className="md:col-span-1">
              <div className="flex items-center mb-4">
                <div className="bg-gradient-to-r from-emerald-500 to-blue-500 p-2 rounded-xl">
                  <Repeat className="h-6 w-6 text-white" />
                </div>
              </div>
              <p className="text-gray-400 text-sm leading-relaxed">
                منصة آمنة وموثوقة لتبادل الأغراض بدون استخدام المال. بدلها بدون فلوس!
              </p>
            </div>

            {/* Quick Links */}
            <div>
              <h4 className="font-semibold mb-4">روابط سريعة</h4>
              <ul className="space-y-2 text-sm text-gray-400">
                <li><Link href="/browse" className="hover:text-emerald-400 transition-colors">تصفح الأغراض</Link></li>
                <li><Link href="/add-item" className="hover:text-emerald-400 transition-colors">أضف غرض</Link></li>
                <li><Link href="/how-it-works" className="hover:text-emerald-400 transition-colors">كيف يعمل</Link></li>
                <li><Link href="/about" className="hover:text-emerald-400 transition-colors">من نحن</Link></li>
              </ul>
            </div>

            {/* Support */}
            <div>
              <h4 className="font-semibold mb-4">الدعم</h4>
              <ul className="space-y-2 text-sm text-gray-400">
                <li><Link href="/faq" className="hover:text-emerald-400 transition-colors">الأسئلة الشائعة</Link></li>
                <li><Link href="/contact" className="hover:text-emerald-400 transition-colors">اتصل بنا</Link></li>
                <li><Link href="/privacy" className="hover:text-emerald-400 transition-colors">سياسة الخصوصية</Link></li>
                <li><Link href="/terms" className="hover:text-emerald-400 transition-colors">شروط الاستخدام</Link></li>
              </ul>
            </div>

            {/* Account */}
            <div>
              <h4 className="font-semibold mb-4">الحساب</h4>
              <ul className="space-y-2 text-sm text-gray-400">
                <li><Link href="/login" className="hover:text-emerald-400 transition-colors">تسجيل الدخول</Link></li>
                <li><Link href="/register" className="hover:text-emerald-400 transition-colors">إنشاء حساب</Link></li>
                <li><Link href="/profile" className="hover:text-emerald-400 transition-colors">الملف الشخصي</Link></li>
                <li><Link href="/messages" className="hover:text-emerald-400 transition-colors">الرسائل</Link></li>
              </ul>
            </div>
          </div>

          {/* Bottom Section */}
          <div className="border-t border-gray-800 mt-8 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <div className="text-sm text-gray-400 mb-4 md:mb-0">
                © 2025 جميع الحقوق محفوظة
              </div>

              <div className="text-xs text-gray-500">
                مطور بـ ❤️ في السعودية
              </div>
            </div>
          </div>
        </div>
      </footer>
    );
  }

  // Simple footer
  return (
    <footer className="bg-gray-900 text-white mt-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">
          <div className="flex items-center justify-center mb-4">
            <div className="bg-gradient-to-r from-emerald-500 to-blue-500 p-2 rounded-xl">
              <Repeat className="h-6 w-6 text-white" />
            </div>
          </div>
          <p className="text-gray-400 text-sm mb-6">
            © 2025 جميع الحقوق محفوظة
          </p>
          <div className="text-xs text-gray-500">
            مطور بـ ❤️ في السعودية
          </div>
        </div>
      </div>
    </footer>
  );
}

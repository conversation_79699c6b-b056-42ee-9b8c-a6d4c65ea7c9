import Link from "next/link";
import { 
  Repeat, 
  Heart, 
  Users, 
  Globe, 
  Target, 
  Lightbulb,
  Award,
  Leaf,
  Handshake,
  ArrowRight
} from "lucide-react";

const teamMembers = [
  {
    name: "أحمد محمد",
    role: "المؤسس والرئيس التنفيذي",
    description: "خبير في التكنولوجيا والاستدامة، يؤمن بقوة المجتمع في إحداث التغيير الإيجابي.",
    image: "/api/placeholder/150/150"
  },
  {
    name: "فاطمة أحمد",
    role: "مديرة التطوير",
    description: "مطورة متخصصة في تجربة المستخدم، تسعى لجعل التكنولوجيا أكثر إنسانية.",
    image: "/api/placeholder/150/150"
  },
  {
    name: "محمد علي",
    role: "مدير التسويق",
    description: "خبير في التسويق الرقمي والمجتمعات، يركز على بناء علاقات طويلة المدى.",
    image: "/api/placeholder/150/150"
  }
];

const values = [
  {
    icon: <Leaf className="h-8 w-8" />,
    title: "الاستدامة",
    description: "نؤمن بأهمية الحفاظ على البيئة من خلال إعادة استخدام الأغراض وتقليل الهدر"
  },
  {
    icon: <Users className="h-8 w-8" />,
    title: "المجتمع",
    description: "نبني مجتمعاً متماسكاً يساعد أفراده بعضهم البعض ويتشاركون الموارد"
  },
  {
    icon: <Handshake className="h-8 w-8" />,
    title: "الثقة",
    description: "نضع الثقة والشفافية في مقدمة أولوياتنا لضمان تجربة آمنة للجميع"
  },
  {
    icon: <Heart className="h-8 w-8" />,
    title: "الإنسانية",
    description: "نسعى لجعل التبادل تجربة إنسانية تربط بين الناس وتخلق علاقات جديدة"
  }
];

const stats = [
  { number: "10,000+", label: "مستخدم نشط" },
  { number: "25,000+", label: "عملية تبادل" },
  { number: "15", label: "مدينة" },
  { number: "95%", label: "رضا المستخدمين" }
];

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-blue-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-md border-b border-emerald-100 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="flex items-center space-x-2">
              <div className="bg-gradient-to-r from-emerald-500 to-blue-500 p-2 rounded-xl">
                <Repeat className="h-6 w-6 text-white" />
              </div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-emerald-600 to-blue-600 bg-clip-text text-transparent">
                مقايضة
              </h1>
            </Link>
            
            <nav className="hidden md:flex items-center space-x-8">
              <Link href="/browse" className="text-gray-700 hover:text-emerald-600 transition-colors">
                تصفح الأغراض
              </Link>
              <Link href="/how-it-works" className="text-gray-700 hover:text-emerald-600 transition-colors">
                كيف يعمل
              </Link>
              <Link href="/about" className="text-emerald-600 font-semibold">
                من نحن
              </Link>
            </nav>

            <div className="flex items-center space-x-4">
              <Link 
                href="/login" 
                className="text-gray-700 hover:text-emerald-600 transition-colors"
              >
                تسجيل الدخول
              </Link>
              <Link 
                href="/register" 
                className="bg-gradient-to-r from-emerald-500 to-blue-500 text-white px-6 py-2 rounded-full hover:shadow-lg transition-all duration-300 transform hover:scale-105"
              >
                ابدأ المقايضة
              </Link>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Hero Section */}
        <div className="text-center mb-20">
          <div className="bg-gradient-to-r from-emerald-100 to-blue-100 p-4 rounded-full w-20 h-20 mx-auto mb-8 flex items-center justify-center">
            <Globe className="h-10 w-10 text-emerald-600" />
          </div>
          
          <h2 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
            <span className="bg-gradient-to-r from-emerald-600 to-blue-600 bg-clip-text text-transparent">
              من نحن
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            نحن فريق شغوف يؤمن بقوة المجتمع والاستدامة. أسسنا مقايضة لنجعل التبادل أسهل وأكثر أماناً، 
            ولنساهم في بناء عالم أكثر استدامة من خلال إعادة استخدام الأغراض.
          </p>
        </div>

        {/* Mission & Vision */}
        <div className="grid md:grid-cols-2 gap-12 mb-20">
          <div className="bg-white/60 backdrop-blur-sm rounded-2xl border border-emerald-100 p-8">
            <div className="bg-emerald-100 p-3 rounded-xl w-fit mb-6">
              <Target className="h-8 w-8 text-emerald-600" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-4">رسالتنا</h3>
            <p className="text-gray-700 leading-relaxed">
              تمكين الأفراد من تبادل الأغراض بطريقة آمنة وسهلة، وبناء مجتمع مستدام يقدر قيمة الأشياء 
              ويؤمن بأن ما لا نحتاجه قد يكون كنزاً لشخص آخر. نسعى لتقليل الهدر وتعزيز ثقافة المشاركة.
            </p>
          </div>

          <div className="bg-white/60 backdrop-blur-sm rounded-2xl border border-blue-100 p-8">
            <div className="bg-blue-100 p-3 rounded-xl w-fit mb-6">
              <Lightbulb className="h-8 w-8 text-blue-600" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-4">رؤيتنا</h3>
            <p className="text-gray-700 leading-relaxed">
              أن نصبح المنصة الرائدة للتبادل في المنطقة، ونساهم في تغيير نظرة المجتمع للاستهلاك والملكية. 
              نحلم بعالم يقدر فيه الناس قيمة الأشياء ويتشاركونها بدلاً من إهدارها.
            </p>
          </div>
        </div>

        {/* Stats */}
        <div className="bg-gradient-to-r from-emerald-500 to-blue-500 rounded-2xl p-8 mb-20">
          <h3 className="text-2xl font-bold text-white text-center mb-8">إنجازاتنا بالأرقام</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-white mb-2">{stat.number}</div>
                <div className="text-emerald-100">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>

        {/* Values */}
        <div className="mb-20">
          <h3 className="text-3xl font-bold text-gray-900 text-center mb-12">قيمنا الأساسية</h3>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <div key={index} className="bg-white/60 backdrop-blur-sm rounded-2xl border border-emerald-100 p-6 text-center hover:shadow-lg transition-all duration-300 transform hover:scale-105">
                <div className="bg-emerald-100 p-3 rounded-xl w-fit mx-auto mb-4 text-emerald-600">
                  {value.icon}
                </div>
                <h4 className="text-lg font-bold text-gray-900 mb-3">{value.title}</h4>
                <p className="text-gray-600 text-sm leading-relaxed">{value.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Team */}
        <div className="mb-20">
          <h3 className="text-3xl font-bold text-gray-900 text-center mb-12">فريق العمل</h3>
          <div className="grid md:grid-cols-3 gap-8">
            {teamMembers.map((member, index) => (
              <div key={index} className="bg-white/60 backdrop-blur-sm rounded-2xl border border-emerald-100 p-6 text-center hover:shadow-lg transition-all duration-300">
                <div className="w-24 h-24 bg-gradient-to-br from-emerald-100 to-blue-100 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <Users className="h-12 w-12 text-gray-600" />
                </div>
                <h4 className="text-xl font-bold text-gray-900 mb-2">{member.name}</h4>
                <p className="text-emerald-600 font-medium mb-3">{member.role}</p>
                <p className="text-gray-600 text-sm leading-relaxed">{member.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Story */}
        <div className="bg-white/60 backdrop-blur-sm rounded-2xl border border-emerald-100 p-8 mb-20">
          <h3 className="text-3xl font-bold text-gray-900 mb-6">قصتنا</h3>
          <div className="prose prose-lg max-w-none text-gray-700">
            <p className="mb-4">
              بدأت فكرة مقايضة من تجربة شخصية بسيطة. كان لدى أحد مؤسسي المنصة جهاز كمبيوتر قديم لا يحتاجه، 
              وفي نفس الوقت كان يبحث عن دراجة هوائية. بدلاً من بيع الكمبيوتر وشراء الدراجة، فكر في إمكانية 
              التبادل المباشر مع شخص آخر.
            </p>
            <p className="mb-4">
              هذا التفكير البسيط أدى إلى إدراك أن هناك الكثير من الناس الذين يواجهون نفس المشكلة: لديهم أشياء 
              لا يحتاجونها ويريدون أشياء أخرى. المشكلة الوحيدة كانت في إيجاد هؤلاء الأشخاص والتواصل معهم بطريقة آمنة.
            </p>
            <p>
              من هنا وُلدت مقايضة - منصة تجمع بين الأشخاص الذين يريدون التبادل، وتوفر لهم بيئة آمنة وسهلة 
              للتواصل وإتمام عمليات التبادل. اليوم، نفخر بأننا ساعدنا آلاف الأشخاص في العثور على ما يحتاجونه 
              والتخلص مما لا يحتاجونه بطريقة مفيدة ومستدامة.
            </p>
          </div>
        </div>

        {/* CTA */}
        <div className="text-center">
          <div className="bg-gradient-to-r from-emerald-500 to-blue-500 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">انضم إلى مجتمعنا اليوم</h3>
            <p className="mb-6 opacity-90">
              كن جزءاً من التغيير الإيجابي وساهم في بناء مجتمع أكثر استدامة
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/register"
                className="bg-white text-emerald-600 px-8 py-3 rounded-xl font-semibold hover:shadow-lg transition-all duration-300 transform hover:scale-105 inline-flex items-center justify-center"
              >
                ابدأ المقايضة الآن
                <ArrowRight className="mr-2 h-5 w-5" />
              </Link>
              <Link
                href="/browse"
                className="border-2 border-white text-white px-8 py-3 rounded-xl font-semibold hover:bg-white hover:text-emerald-600 transition-all duration-300"
              >
                تصفح الأغراض
              </Link>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}

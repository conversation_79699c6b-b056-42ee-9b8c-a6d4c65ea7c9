"use client";

import Image from "next/image";
import Link from "next/link";
import { Search, ArrowRight, Repeat, Shield, Users, Star, Save } from "lucide-react";
import Footer from "../components/Footer";

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-blue-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-md border-b border-emerald-100 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <div className="bg-gradient-to-r from-emerald-500 to-blue-500 p-2 rounded-xl">
                <Repeat className="h-6 w-6 text-white" />
              </div>
            </div>

            <nav className="hidden md:flex items-center space-x-8">
              <Link href="/browse" className="text-gray-700 hover:text-emerald-600 transition-colors">
                تصفح الأغراض
              </Link>
              <Link href="/how-it-works" className="text-gray-700 hover:text-emerald-600 transition-colors">
                كيف يعمل
              </Link>
              <Link href="/about" className="text-gray-700 hover:text-emerald-600 transition-colors">
                من نحن
              </Link>
            </nav>

            <div className="flex items-center space-x-4">
              <Link
                href="/login"
                className="text-gray-700 hover:text-emerald-600 transition-colors"
              >
                تسجيل الدخول
              </Link>
              <Link
                href="/register"
                className="bg-gradient-to-r from-emerald-500 to-blue-500 text-white px-6 py-2 rounded-full hover:shadow-lg transition-all duration-300 transform hover:scale-105"
              >
                ابدأ المقايضة
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center mb-16">
          <h2 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
            <span className="bg-gradient-to-r from-emerald-600 to-blue-600 bg-clip-text text-transparent">
              بدلها بدون فلوس
            </span>
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
            منصة المقايضة الأولى في المنطقة. بدل أغراضك القديمة بأشياء جديدة تحتاجها،
            وساهم في بناء مجتمع مستدام وصديق للبيئة
          </p>

          {/* Search Bar */}
          <div className="max-w-2xl mx-auto mb-8">
            <div className="relative">
              <Search className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                type="text"
                placeholder="ابحث عن غرض أو اكتب ماذا تريد..."
                className="w-full pr-12 pl-6 py-4 text-lg border-2 border-emerald-200 rounded-2xl focus:border-emerald-500 focus:outline-none bg-white/80 backdrop-blur-sm"
              />
              <button className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-gradient-to-r from-emerald-500 to-blue-500 text-white px-6 py-2 rounded-xl hover:shadow-lg transition-all duration-300">
                بحث
              </button>
            </div>
          </div>

          <Link
            href="/browse"
            className="inline-flex items-center bg-gradient-to-r from-emerald-500 to-blue-500 text-white px-8 py-4 rounded-2xl text-lg font-semibold hover:shadow-xl transition-all duration-300 transform hover:scale-105"
          >
            ابدأ المقايضة الآن
            <ArrowRight className="mr-2 h-5 w-5" />
          </Link>
        </div>

        {/* Features Section */}
        <div className="grid md:grid-cols-3 gap-8 mb-16">
          <div className="bg-white/60 backdrop-blur-sm p-8 rounded-2xl border border-emerald-100 hover:shadow-lg transition-all duration-300 transform hover:scale-105">
            <div className="bg-emerald-100 p-3 rounded-xl w-fit mb-4">
              <Shield className="h-8 w-8 text-emerald-600" />
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-3">آمن وموثوق</h3>
            <p className="text-gray-600">
              نظام تقييم متقدم وحماية للمستخدمين لضمان تجربة مقايضة آمنة وموثوقة
            </p>
          </div>

          <div className="bg-white/60 backdrop-blur-sm p-8 rounded-2xl border border-blue-100 hover:shadow-lg transition-all duration-300 transform hover:scale-105">
            <div className="bg-blue-100 p-3 rounded-xl w-fit mb-4">
              <Users className="h-8 w-8 text-blue-600" />
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-3">مجتمع نشط</h3>
            <p className="text-gray-600">
              انضم لآلاف المستخدمين الذين يتبادلون الأغراض يومياً في جميع أنحاء المنطقة
            </p>
          </div>

          <div className="bg-white/60 backdrop-blur-sm p-8 rounded-2xl border border-emerald-100 hover:shadow-lg transition-all duration-300 transform hover:scale-105">
            <div className="bg-emerald-100 p-3 rounded-xl w-fit mb-4">
              <Star className="h-8 w-8 text-emerald-600" />
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-3">سهل الاستخدام</h3>
            <p className="text-gray-600">
              واجهة بسيطة وسهلة تجعل عملية المقايضة ممتعة وسريعة للجميع
            </p>
          </div>
        </div>
      </main>

      {/* Footer with Save Button */}
      <footer className="bg-gray-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid md:grid-cols-4 gap-8">
            {/* Brand */}
            <div className="md:col-span-1">
              <div className="flex items-center mb-4">
                <div className="bg-gradient-to-r from-emerald-500 to-blue-500 p-2 rounded-xl">
                  <Repeat className="h-6 w-6 text-white" />
                </div>
              </div>
              <p className="text-gray-400 text-sm leading-relaxed">
                منصة آمنة وموثوقة لتبادل الأغراض بدون استخدام المال. بدلها بدون فلوس!
              </p>
            </div>

            {/* Quick Links */}
            <div>
              <h4 className="font-semibold mb-4">روابط سريعة</h4>
              <ul className="space-y-2 text-sm text-gray-400">
                <li><Link href="/browse" className="hover:text-emerald-400 transition-colors">تصفح الأغراض</Link></li>
                <li><Link href="/add-item" className="hover:text-emerald-400 transition-colors">أضف غرض</Link></li>
                <li><Link href="/how-it-works" className="hover:text-emerald-400 transition-colors">كيف يعمل</Link></li>
                <li><Link href="/about" className="hover:text-emerald-400 transition-colors">من نحن</Link></li>
              </ul>
            </div>

            {/* Support */}
            <div>
              <h4 className="font-semibold mb-4">الدعم</h4>
              <ul className="space-y-2 text-sm text-gray-400">
                <li><Link href="/faq" className="hover:text-emerald-400 transition-colors">الأسئلة الشائعة</Link></li>
                <li><Link href="/contact" className="hover:text-emerald-400 transition-colors">اتصل بنا</Link></li>
                <li><Link href="/privacy" className="hover:text-emerald-400 transition-colors">سياسة الخصوصية</Link></li>
                <li><Link href="/terms" className="hover:text-emerald-400 transition-colors">شروط الاستخدام</Link></li>
              </ul>
            </div>

            {/* Account */}
            <div>
              <h4 className="font-semibold mb-4">الحساب</h4>
              <ul className="space-y-2 text-sm text-gray-400">
                <li><Link href="/login" className="hover:text-emerald-400 transition-colors">تسجيل الدخول</Link></li>
                <li><Link href="/register" className="hover:text-emerald-400 transition-colors">إنشاء حساب</Link></li>
                <li><Link href="/profile" className="hover:text-emerald-400 transition-colors">الملف الشخصي</Link></li>
                <li><Link href="/messages" className="hover:text-emerald-400 transition-colors">الرسائل</Link></li>
              </ul>
            </div>
          </div>

          {/* Bottom Section with Save Button */}
          <div className="border-t border-gray-800 mt-8 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <div className="text-sm text-gray-400 mb-4 md:mb-0">
                © 2025 جميع الحقوق محفوظة
              </div>

              <div className="flex items-center space-x-4">
                <button
                  onClick={() => {
                    localStorage.setItem('moqayada_preferences', JSON.stringify({
                      theme: 'default',
                      language: 'ar',
                      notifications: true,
                      savedAt: new Date().toISOString()
                    }));
                    alert('تم حفظ إعداداتك بنجاح!');
                  }}
                  className="bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center"
                >
                  <Save className="h-4 w-4 ml-1" />
                  حفظ الإعدادات
                </button>

                <div className="text-xs text-gray-500">
                  مطور بـ ❤️ في السعودية
                </div>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}

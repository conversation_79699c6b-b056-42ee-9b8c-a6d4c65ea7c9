import Link from "next/link";
import { Search, Filter, MapPin, Heart, ArrowRight, Repeat } from "lucide-react";

// Mock data for demonstration
const mockItems = [
  {
    id: 1,
    title: "آيفون 12 مستعمل",
    description: "حالة ممتازة، مع الشاحن والعلبة",
    wantedItem: "لابتوب أو تابلت",
    location: "الرياض",
    image: "/api/placeholder/300/200",
    condition: "ممتاز",
    category: "إلكترونيات"
  },
  {
    id: 2,
    title: "طاولة خشبية كلاسيكية",
    description: "طاولة طعام خشبية لـ 6 أشخاص",
    wantedItem: "كنبة أو كراسي",
    location: "جدة",
    image: "/api/placeholder/300/200",
    condition: "جيد",
    category: "أثاث"
  },
  {
    id: 3,
    title: "دراجة هوائية",
    description: "دراجة رياضية للكبار، مقاس 26",
    wantedItem: "معدات رياضية",
    location: "الدمام",
    image: "/api/placeholder/300/200",
    condition: "جيد جداً",
    category: "رياضة"
  },
  {
    id: 4,
    title: "كتب جامعية",
    description: "مجموعة كتب هندسة، طبعات حديثة",
    wantedItem: "كتب طبية أو علمية",
    location: "الرياض",
    image: "/api/placeholder/300/200",
    condition: "ممتاز",
    category: "كتب"
  },
  {
    id: 5,
    title: "ساعة ذكية",
    description: "Apple Watch Series 7، مع الشاحن",
    wantedItem: "سماعات لاسلكية",
    location: "الخبر",
    image: "/api/placeholder/300/200",
    condition: "ممتاز",
    category: "إلكترونيات"
  },
  {
    id: 6,
    title: "ألعاب أطفال",
    description: "مجموعة ألعاب تعليمية للأطفال",
    wantedItem: "ملابس أطفال أو كتب",
    location: "مكة",
    image: "/api/placeholder/300/200",
    condition: "جيد",
    category: "أطفال"
  }
];

const categories = [
  "الكل",
  "إلكترونيات",
  "أثاث",
  "ملابس",
  "كتب",
  "رياضة",
  "أطفال",
  "أدوات منزلية",
  "سيارات",
  "أخرى"
];

export default function BrowsePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-blue-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-md border-b border-emerald-100 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="flex items-center">
              <div className="bg-gradient-to-r from-emerald-500 to-blue-500 p-2 rounded-xl">
                <Repeat className="h-6 w-6 text-white" />
              </div>
            </Link>

            <nav className="hidden md:flex items-center space-x-8">
              <Link href="/browse" className="text-emerald-600 font-semibold">
                تصفح الأغراض
              </Link>
              <Link href="/add-item" className="text-gray-700 hover:text-emerald-600 transition-colors">
                أضف غرض
              </Link>
              <Link href="/messages" className="text-gray-700 hover:text-emerald-600 transition-colors">
                الرسائل
              </Link>
            </nav>

            <div className="flex items-center space-x-4">
              <Link
                href="/profile"
                className="text-gray-700 hover:text-emerald-600 transition-colors"
              >
                حسابي
              </Link>
              <Link
                href="/add-item"
                className="bg-gradient-to-r from-emerald-500 to-blue-500 text-white px-6 py-2 rounded-full hover:shadow-lg transition-all duration-300 transform hover:scale-105"
              >
                أضف غرض
              </Link>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search and Filter Section */}
        <div className="mb-8">
          <div className="flex flex-col lg:flex-row gap-4 mb-6">
            {/* Search Bar */}
            <div className="flex-1 relative">
              <Search className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                type="text"
                placeholder="ابحث عن غرض..."
                className="w-full pr-12 pl-6 py-3 border-2 border-emerald-200 rounded-xl focus:border-emerald-500 focus:outline-none bg-white/80 backdrop-blur-sm"
              />
            </div>

            {/* Location Filter */}
            <div className="relative">
              <MapPin className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <select className="pr-10 pl-4 py-3 border-2 border-emerald-200 rounded-xl focus:border-emerald-500 focus:outline-none bg-white/80 backdrop-blur-sm min-w-[150px]">
                <option>كل المدن</option>
                <option>الرياض</option>
                <option>جدة</option>
                <option>الدمام</option>
                <option>مكة</option>
                <option>المدينة</option>
              </select>
            </div>

            {/* Advanced Filter Button */}
            <button className="flex items-center px-6 py-3 border-2 border-emerald-200 rounded-xl hover:border-emerald-500 transition-colors bg-white/80 backdrop-blur-sm">
              <Filter className="h-5 w-5 text-gray-600 ml-2" />
              فلترة متقدمة
            </button>
          </div>

          {/* Categories */}
          <div className="flex flex-wrap gap-2">
            {categories.map((category, index) => (
              <button
                key={index}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
                  index === 0
                    ? "bg-gradient-to-r from-emerald-500 to-blue-500 text-white shadow-lg"
                    : "bg-white/60 text-gray-700 hover:bg-emerald-100 border border-emerald-200"
                }`}
              >
                {category}
              </button>
            ))}
          </div>
        </div>

        {/* Results Header */}
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900">
            الأغراض المتاحة للمقايضة
            <span className="text-lg font-normal text-gray-600 mr-2">({mockItems.length} غرض)</span>
          </h2>

          <select className="px-4 py-2 border border-emerald-200 rounded-lg focus:border-emerald-500 focus:outline-none bg-white/80">
            <option>الأحدث أولاً</option>
            <option>الأقدم أولاً</option>
            <option>الأقرب للموقع</option>
          </select>
        </div>

        {/* Items Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {mockItems.map((item) => (
            <div
              key={item.id}
              className="bg-white/60 backdrop-blur-sm rounded-2xl border border-emerald-100 overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:scale-105"
            >
              {/* Item Image */}
              <div className="relative h-48 bg-gradient-to-br from-emerald-100 to-blue-100">
                <div className="absolute inset-0 flex items-center justify-center text-gray-500">
                  صورة الغرض
                </div>
                <button className="absolute top-3 left-3 p-2 bg-white/80 rounded-full hover:bg-white transition-colors">
                  <Heart className="h-5 w-5 text-gray-600" />
                </button>
                <div className="absolute top-3 right-3 bg-emerald-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                  {item.condition}
                </div>
              </div>

              {/* Item Details */}
              <div className="p-6">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-emerald-600 font-medium">{item.category}</span>
                  <div className="flex items-center text-sm text-gray-500">
                    <MapPin className="h-4 w-4 ml-1" />
                    {item.location}
                  </div>
                </div>

                <h3 className="text-lg font-bold text-gray-900 mb-2">{item.title}</h3>
                <p className="text-gray-600 text-sm mb-4 line-clamp-2">{item.description}</p>

                <div className="bg-blue-50 p-3 rounded-lg mb-4">
                  <p className="text-sm text-blue-800">
                    <span className="font-medium">يريد في المقابل:</span> {item.wantedItem}
                  </p>
                </div>

                <Link
                  href={`/items/${item.id}`}
                  className="w-full bg-gradient-to-r from-emerald-500 to-blue-500 text-white py-3 rounded-xl font-medium hover:shadow-lg transition-all duration-300 flex items-center justify-center"
                >
                  عرض التفاصيل
                  <ArrowRight className="mr-2 h-4 w-4" />
                </Link>
              </div>
            </div>
          ))}
        </div>

        {/* Load More Button */}
        <div className="text-center mt-12">
          <button className="bg-white/60 backdrop-blur-sm border-2 border-emerald-200 text-emerald-600 px-8 py-3 rounded-xl font-medium hover:bg-emerald-50 transition-all duration-300">
            تحميل المزيد
          </button>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 text-white mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="flex items-center justify-center mb-4">
              <div className="bg-gradient-to-r from-emerald-500 to-blue-500 p-2 rounded-xl">
                <Repeat className="h-6 w-6 text-white" />
              </div>
            </div>
            <p className="text-gray-400 text-sm mb-6">
              © 2025 جميع الحقوق محفوظة
            </p>
            <div className="text-xs text-gray-500">
              مطور بـ ❤️ في السعودية
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}

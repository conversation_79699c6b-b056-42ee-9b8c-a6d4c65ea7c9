import Link from "next/link";
import { 
  Repeat, 
  User, 
  Star, 
  MapPin, 
  Calendar, 
  MessageCircle,
  Shield,
  Package,
  TrendingUp,
  Eye,
  Heart,
  Flag,
  ArrowRight,
  CheckCircle
} from "lucide-react";

// Mock user data - في التطبيق الحقيقي، ستأتي من قاعدة البيانات
const getUserData = (username: string) => {
  // محاكاة بيانات المستخدم
  return {
    name: decodeURIComponent(username),
    location: "الرياض، المملكة العربية السعودية",
    joinDate: "انضم في يناير 2024",
    verified: true,
    rating: 4.8,
    reviewsCount: 23,
    completedExchanges: 15,
    responseTime: "خلال ساعة",
    lastSeen: "متصل الآن",
    avatar: "/api/placeholder/120/120"
  };
};

// Mock user items
const userItems = [
  {
    id: 1,
    title: "آيفون 12 مستعمل",
    description: "حالة ممتازة، مع الشاحن والعلبة",
    wantedItem: "لابتوب أو تابلت",
    status: "متاح",
    views: 45,
    interested: 8,
    postedDate: "منذ 3 أيام",
    image: "/api/placeholder/200/150"
  },
  {
    id: 2,
    title: "ساعة ذكية Apple Watch",
    description: "Series 7، مع الشاحن",
    wantedItem: "سماعات لاسلكية",
    status: "قيد التفاوض",
    views: 32,
    interested: 5,
    postedDate: "منذ أسبوع",
    image: "/api/placeholder/200/150"
  },
  {
    id: 3,
    title: "كتب هندسة",
    description: "مجموعة كتب جامعية",
    wantedItem: "كتب طبية",
    status: "متاح",
    views: 28,
    interested: 3,
    postedDate: "منذ أسبوعين",
    image: "/api/placeholder/200/150"
  }
];

// Mock reviews
const userReviews = [
  {
    id: 1,
    reviewer: "فاطمة أحمد",
    rating: 5,
    comment: "تعامل ممتاز وصادق، الغرض كان كما وُصف تماماً. أنصح بالتعامل معه بشدة.",
    date: "منذ أسبوع",
    item: "طاولة خشبية",
    verified: true
  },
  {
    id: 2,
    reviewer: "محمد علي",
    rating: 5,
    comment: "شخص موثوق ومتعاون، التقينا في الوقت المحدد وكان التبادل سلساً جداً.",
    date: "منذ أسبوعين",
    item: "دراجة هوائية",
    verified: true
  },
  {
    id: 3,
    reviewer: "سارة خالد",
    rating: 4,
    comment: "تجربة جيدة بشكل عام، لكن كان هناك تأخير بسيط في الموعد المحدد.",
    date: "منذ شهر",
    item: "كتب جامعية",
    verified: false
  }
];

const stats = [
  { icon: <Package className="h-6 w-6" />, label: "أغراض منشورة", value: "8" },
  { icon: <TrendingUp className="h-6 w-6" />, label: "مقايضات مكتملة", value: "15" },
  { icon: <Eye className="h-6 w-6" />, label: "مشاهدات الملف", value: "234" },
  { icon: <Heart className="h-6 w-6" />, label: "تقييمات إيجابية", value: "22" }
];

export default function PublicProfilePage({ params }: { params: { username: string } }) {
  const userData = getUserData(params.username);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "متاح": return "bg-green-100 text-green-800";
      case "قيد التفاوض": return "bg-yellow-100 text-yellow-800";
      case "تم التبادل": return "bg-gray-100 text-gray-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-blue-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-md border-b border-emerald-100 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="flex items-center space-x-2">
              <div className="bg-gradient-to-r from-emerald-500 to-blue-500 p-2 rounded-xl">
                <Repeat className="h-6 w-6 text-white" />
              </div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-emerald-600 to-blue-600 bg-clip-text text-transparent">
                مقايضة
              </h1>
            </Link>
            
            <nav className="hidden md:flex items-center space-x-8">
              <Link href="/browse" className="text-gray-700 hover:text-emerald-600 transition-colors">
                تصفح الأغراض
              </Link>
              <Link href="/add-item" className="text-gray-700 hover:text-emerald-600 transition-colors">
                أضف غرض
              </Link>
              <Link href="/messages" className="text-gray-700 hover:text-emerald-600 transition-colors">
                الرسائل
              </Link>
            </nav>

            <div className="flex items-center space-x-4">
              <Link 
                href="/profile" 
                className="text-gray-700 hover:text-emerald-600 transition-colors"
              >
                حسابي
              </Link>
              <Link 
                href="/add-item" 
                className="bg-gradient-to-r from-emerald-500 to-blue-500 text-white px-6 py-2 rounded-full hover:shadow-lg transition-all duration-300 transform hover:scale-105"
              >
                أضف غرض
              </Link>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <div className="flex items-center text-sm text-gray-600 mb-6">
          <Link href="/" className="hover:text-emerald-600">الرئيسية</Link>
          <ArrowRight className="h-4 w-4 mx-2 rotate-180" />
          <Link href="/browse" className="hover:text-emerald-600">المستخدمين</Link>
          <ArrowRight className="h-4 w-4 mx-2 rotate-180" />
          <span className="text-gray-900">{userData.name}</span>
        </div>

        {/* Profile Header */}
        <div className="bg-white/60 backdrop-blur-sm rounded-2xl border border-emerald-100 p-8 mb-8">
          <div className="flex flex-col md:flex-row items-center md:items-start space-y-6 md:space-y-0 md:space-x-8">
            {/* Avatar */}
            <div className="relative">
              <div className="w-32 h-32 bg-gradient-to-br from-emerald-100 to-blue-100 rounded-full flex items-center justify-center">
                <User className="h-16 w-16 text-gray-600" />
              </div>
              {userData.verified && (
                <div className="absolute -bottom-2 -left-2 bg-emerald-500 p-2 rounded-full">
                  <Shield className="h-4 w-4 text-white" />
                </div>
              )}
              <div className="absolute -top-2 -right-2 bg-green-500 w-6 h-6 rounded-full border-4 border-white"></div>
            </div>

            {/* User Info */}
            <div className="flex-1 text-center md:text-right">
              <div className="flex items-center justify-center md:justify-start space-x-3 mb-2">
                <h2 className="text-3xl font-bold text-gray-900">{userData.name}</h2>
                {userData.verified && (
                  <div className="bg-emerald-100 p-1 rounded-full" title="حساب موثق">
                    <CheckCircle className="h-5 w-5 text-emerald-600" />
                  </div>
                )}
              </div>
              
              <div className="flex items-center justify-center md:justify-start space-x-2 mb-3">
                <Star className="h-5 w-5 text-yellow-400" />
                <span className="font-semibold text-gray-900">{userData.rating}</span>
                <span className="text-gray-600">({userData.reviewsCount} تقييم)</span>
              </div>

              <div className="flex items-center justify-center md:justify-start space-x-2 mb-2 text-gray-600">
                <MapPin className="h-4 w-4" />
                <span>{userData.location}</span>
              </div>

              <div className="flex items-center justify-center md:justify-start space-x-2 mb-2 text-gray-600">
                <Calendar className="h-4 w-4" />
                <span>{userData.joinDate}</span>
              </div>

              <div className="text-sm text-emerald-600 mb-4">
                {userData.lastSeen} • يرد خلال {userData.responseTime}
              </div>

              <div className="flex flex-col sm:flex-row gap-3 justify-center md:justify-start">
                <Link
                  href="/messages"
                  className="bg-emerald-500 text-white px-6 py-2 rounded-xl font-medium hover:bg-emerald-600 transition-colors inline-flex items-center justify-center"
                >
                  <MessageCircle className="h-4 w-4 ml-2" />
                  إرسال رسالة
                </Link>
                <button className="border-2 border-gray-200 text-gray-600 px-6 py-2 rounded-xl font-medium hover:bg-gray-50 transition-colors inline-flex items-center justify-center">
                  <Flag className="h-4 w-4 ml-2" />
                  إبلاغ
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
          {stats.map((stat, index) => (
            <div key={index} className="bg-white/60 backdrop-blur-sm rounded-2xl border border-emerald-100 p-6 text-center">
              <div className="bg-emerald-100 p-3 rounded-xl w-fit mx-auto mb-3 text-emerald-600">
                {stat.icon}
              </div>
              <div className="text-2xl font-bold text-gray-900 mb-1">{stat.value}</div>
              <div className="text-sm text-gray-600">{stat.label}</div>
            </div>
          ))}
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* User Items */}
          <div className="lg:col-span-2">
            <div className="bg-white/60 backdrop-blur-sm rounded-2xl border border-emerald-100 p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-6">أغراض {userData.name} ({userItems.length})</h3>
              
              <div className="grid md:grid-cols-2 gap-6">
                {userItems.map((item) => (
                  <div key={item.id} className="border border-emerald-200 rounded-xl overflow-hidden hover:shadow-lg transition-shadow">
                    <div className="aspect-video bg-gradient-to-br from-emerald-100 to-blue-100 flex items-center justify-center">
                      <span className="text-gray-500">صورة الغرض</span>
                    </div>
                    
                    <div className="p-4">
                      <div className="flex items-center justify-between mb-2">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(item.status)}`}>
                          {item.status}
                        </span>
                        <span className="text-xs text-gray-500">{item.postedDate}</span>
                      </div>
                      
                      <h4 className="font-semibold text-gray-900 mb-2">{item.title}</h4>
                      <p className="text-sm text-gray-600 mb-3 line-clamp-2">{item.description}</p>
                      
                      <div className="bg-blue-50 p-2 rounded-lg mb-3">
                        <p className="text-xs text-blue-800">
                          <span className="font-medium">يريد:</span> {item.wantedItem}
                        </p>
                      </div>
                      
                      <div className="flex items-center justify-between text-sm text-gray-500">
                        <div className="flex items-center space-x-3">
                          <div className="flex items-center">
                            <Eye className="h-4 w-4 ml-1" />
                            {item.views}
                          </div>
                          <div className="flex items-center">
                            <MessageCircle className="h-4 w-4 ml-1" />
                            {item.interested}
                          </div>
                        </div>
                        <Link
                          href={`/items/${item.id}`}
                          className="text-emerald-600 hover:text-emerald-700 font-medium"
                        >
                          عرض التفاصيل
                        </Link>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Reviews Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white/60 backdrop-blur-sm rounded-2xl border border-emerald-100 p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-6">التقييمات ({userReviews.length})</h3>
              
              <div className="space-y-4">
                {userReviews.map((review) => (
                  <div key={review.id} className="border border-emerald-200 rounded-xl p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center space-x-2">
                        <div className="w-8 h-8 bg-gradient-to-br from-emerald-100 to-blue-100 rounded-full flex items-center justify-center">
                          <User className="h-4 w-4 text-gray-600" />
                        </div>
                        <div>
                          <div className="flex items-center space-x-1">
                            <h4 className="font-medium text-gray-900 text-sm">{review.reviewer}</h4>
                            {review.verified && (
                              <CheckCircle className="h-3 w-3 text-emerald-500" />
                            )}
                          </div>
                          <p className="text-xs text-gray-500">{review.date}</p>
                        </div>
                      </div>
                      
                      <div className="flex items-center">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`h-3 w-3 ${
                              i < review.rating ? "text-yellow-400 fill-current" : "text-gray-300"
                            }`}
                          />
                        ))}
                      </div>
                    </div>
                    
                    <p className="text-sm text-gray-700 mb-2 leading-relaxed">{review.comment}</p>
                    <p className="text-xs text-gray-500">الغرض: {review.item}</p>
                  </div>
                ))}
              </div>

              {/* Safety Tips */}
              <div className="mt-6 bg-yellow-50 border border-yellow-200 rounded-xl p-4">
                <h4 className="font-semibold text-yellow-800 mb-2 text-sm">نصائح للأمان</h4>
                <ul className="text-xs text-yellow-700 space-y-1">
                  <li>• التق في مكان عام وآمن</li>
                  <li>• تأكد من حالة الغرض قبل التبادل</li>
                  <li>• استخدم نظام الرسائل في المنصة</li>
                  <li>• لا تشارك معلومات شخصية حساسة</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}

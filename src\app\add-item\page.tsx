"use client";

import Link from "next/link";
import { useState } from "react";
import { Upload, X, MapPin, Repeat, Camera, Plus } from "lucide-react";

const categories = [
  "إلكترونيات",
  "أثاث",
  "ملابس",
  "كتب",
  "رياضة",
  "أطفال",
  "أدوات منزلية",
  "سيارات",
  "أخرى"
];

const conditions = [
  "جديد",
  "ممتاز",
  "جيد جداً",
  "جيد",
  "مقبول"
];

const cities = [
  "الرياض",
  "جدة",
  "الدمام",
  "مكة",
  "المدينة",
  "الخبر",
  "الطائف",
  "بريدة",
  "تبوك",
  "خميس مشيط"
];

export default function AddItemPage() {
  const [images, setImages] = useState<string[]>([]);
  const [selectedImageIndex, setSelectedImageIndex] = useState<number | null>(null);
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    category: "",
    condition: "",
    wantedItem: "",
    city: "",
    specificLocation: ""
  });

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files) {
      const newImagePromises = Array.from(files).map((file) => {
        return new Promise<string>((resolve) => {
          const reader = new FileReader();
          reader.onload = (e) => {
            resolve(e.target?.result as string);
          };
          reader.readAsDataURL(file);
        });
      });

      Promise.all(newImagePromises).then((newImages) => {
        setImages([...images, ...newImages]);
      });
    }
  };

  const removeImage = (index: number) => {
    setImages(images.filter((_, i) => i !== index));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission
    console.log("Form submitted:", formData, images);
    alert("تم إضافة الغرض بنجاح! سيتم مراجعته قبل النشر.");
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-blue-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-md border-b border-emerald-100 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="flex items-center">
              <div className="bg-gradient-to-r from-emerald-500 to-blue-500 p-2 rounded-xl">
                <Repeat className="h-6 w-6 text-white" />
              </div>
            </Link>

            <nav className="hidden md:flex items-center space-x-8">
              <Link href="/browse" className="text-gray-700 hover:text-emerald-600 transition-colors">
                تصفح الأغراض
              </Link>
              <Link href="/add-item" className="text-emerald-600 font-semibold">
                أضف غرض
              </Link>
              <Link href="/messages" className="text-gray-700 hover:text-emerald-600 transition-colors">
                الرسائل
              </Link>
            </nav>

            <div className="flex items-center space-x-4">
              <Link
                href="/profile"
                className="text-gray-700 hover:text-emerald-600 transition-colors"
              >
                حسابي
              </Link>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white/60 backdrop-blur-sm rounded-2xl border border-emerald-100 p-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-2">أضف غرض للمقايضة</h2>
          <p className="text-gray-600 mb-8">املأ البيانات التالية لإضافة غرضك للمقايضة</p>

          <form onSubmit={handleSubmit} className="space-y-8">
            {/* Image Upload Section */}
            <div>
              <label className="block text-lg font-semibold text-gray-900 mb-4">
                صور الغرض (حتى 5 صور)
              </label>

              <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                {/* Uploaded Images */}
                {images.map((image, index) => (
                  <div key={index} className="relative aspect-square bg-gray-100 rounded-xl overflow-hidden group">
                    <img
                      src={image}
                      alt={`صورة الغرض ${index + 1}`}
                      className="w-full h-full object-cover cursor-pointer"
                      onClick={() => setSelectedImageIndex(index)}
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center">
                      <button
                        type="button"
                        onClick={(e) => {
                          e.stopPropagation();
                          removeImage(index);
                        }}
                        className="bg-red-500 text-white p-2 rounded-full hover:bg-red-600 transition-colors opacity-0 group-hover:opacity-100 transform scale-75 group-hover:scale-100 transition-all duration-200"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    </div>
                    <div className="absolute bottom-2 right-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                      {index + 1}
                    </div>
                  </div>
                ))}

                {/* Upload Button */}
                {images.length < 5 && (
                  <label className="aspect-square border-2 border-dashed border-emerald-300 rounded-xl flex flex-col items-center justify-center cursor-pointer hover:border-emerald-500 transition-colors bg-emerald-50/50">
                    <Upload className="h-8 w-8 text-emerald-500 mb-2" />
                    <span className="text-sm text-emerald-600 font-medium">أضف صورة</span>
                    <input
                      type="file"
                      multiple
                      accept="image/*"
                      onChange={handleImageUpload}
                      className="hidden"
                    />
                  </label>
                )}
              </div>
            </div>

            {/* Basic Information */}
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <label className="block text-lg font-semibold text-gray-900 mb-3">
                  اسم الغرض *
                </label>
                <input
                  type="text"
                  required
                  value={formData.title}
                  onChange={(e) => setFormData({...formData, title: e.target.value})}
                  placeholder="مثال: آيفون 12 مستعمل"
                  className="w-full px-4 py-3 border-2 border-emerald-200 rounded-xl focus:border-emerald-500 focus:outline-none"
                />
              </div>

              <div>
                <label className="block text-lg font-semibold text-gray-900 mb-3">
                  الفئة *
                </label>
                <select
                  required
                  value={formData.category}
                  onChange={(e) => setFormData({...formData, category: e.target.value})}
                  className="w-full px-4 py-3 border-2 border-emerald-200 rounded-xl focus:border-emerald-500 focus:outline-none"
                >
                  <option value="">اختر الفئة</option>
                  {categories.map((category) => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
              </div>
            </div>

            {/* Description */}
            <div>
              <label className="block text-lg font-semibold text-gray-900 mb-3">
                وصف الغرض *
              </label>
              <textarea
                required
                rows={4}
                value={formData.description}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
                placeholder="اكتب وصفاً مفصلاً للغرض، حالته، مميزاته، وأي تفاصيل مهمة..."
                className="w-full px-4 py-3 border-2 border-emerald-200 rounded-xl focus:border-emerald-500 focus:outline-none resize-none"
              />
            </div>

            {/* Condition and Location */}
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <label className="block text-lg font-semibold text-gray-900 mb-3">
                  حالة الغرض *
                </label>
                <select
                  required
                  value={formData.condition}
                  onChange={(e) => setFormData({...formData, condition: e.target.value})}
                  className="w-full px-4 py-3 border-2 border-emerald-200 rounded-xl focus:border-emerald-500 focus:outline-none"
                >
                  <option value="">اختر الحالة</option>
                  {conditions.map((condition) => (
                    <option key={condition} value={condition}>{condition}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-lg font-semibold text-gray-900 mb-3">
                  المدينة *
                </label>
                <select
                  required
                  value={formData.city}
                  onChange={(e) => setFormData({...formData, city: e.target.value})}
                  className="w-full px-4 py-3 border-2 border-emerald-200 rounded-xl focus:border-emerald-500 focus:outline-none"
                >
                  <option value="">اختر المدينة</option>
                  {cities.map((city) => (
                    <option key={city} value={city}>{city}</option>
                  ))}
                </select>
              </div>
            </div>

            {/* Specific Location */}
            <div>
              <label className="block text-lg font-semibold text-gray-900 mb-3">
                الموقع المحدد (اختياري)
              </label>
              <div className="relative">
                <MapPin className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                <input
                  type="text"
                  value={formData.specificLocation}
                  onChange={(e) => setFormData({...formData, specificLocation: e.target.value})}
                  placeholder="مثال: حي النخيل، شارع الملك فهد"
                  className="w-full pr-12 pl-4 py-3 border-2 border-emerald-200 rounded-xl focus:border-emerald-500 focus:outline-none"
                />
              </div>
            </div>

            {/* Wanted Item */}
            <div>
              <label className="block text-lg font-semibold text-gray-900 mb-3">
                ماذا تريد في المقابل؟ *
              </label>
              <textarea
                required
                rows={3}
                value={formData.wantedItem}
                onChange={(e) => setFormData({...formData, wantedItem: e.target.value})}
                placeholder="اكتب ما تريده في مقابل غرضك... مثال: لابتوب، تابلت، أو أي جهاز إلكتروني مشابه"
                className="w-full px-4 py-3 border-2 border-emerald-200 rounded-xl focus:border-emerald-500 focus:outline-none resize-none"
              />
            </div>

            {/* Submit Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 pt-6">
              <button
                type="submit"
                className="flex-1 bg-gradient-to-r from-emerald-500 to-blue-500 text-white py-4 rounded-xl font-semibold hover:shadow-xl transition-all duration-300 transform hover:scale-105"
              >
                نشر الغرض للمقايضة
              </button>

              <Link
                href="/browse"
                className="flex-1 bg-gray-200 text-gray-700 py-4 rounded-xl font-semibold text-center hover:bg-gray-300 transition-colors"
              >
                إلغاء
              </Link>
            </div>
          </form>
        </div>
      </main>

      {/* Image Preview Modal */}
      {selectedImageIndex !== null && (
        <div
          className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4"
          onClick={() => setSelectedImageIndex(null)}
        >
          <div
            className="relative max-w-4xl max-h-full"
            onClick={(e) => e.stopPropagation()}
          >
            <img
              src={images[selectedImageIndex]}
              alt={`صورة الغرض ${selectedImageIndex + 1}`}
              className="max-w-full max-h-full object-contain rounded-lg"
            />

            {/* Close Button */}
            <button
              onClick={() => setSelectedImageIndex(null)}
              className="absolute top-4 left-4 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-75 transition-colors"
            >
              <X className="h-6 w-6" />
            </button>

            {/* Image Counter */}
            <div className="absolute bottom-4 right-4 bg-black bg-opacity-50 text-white px-3 py-1 rounded-full text-sm">
              {selectedImageIndex + 1} من {images.length}
            </div>

            {/* Navigation Arrows */}
            {images.length > 1 && (
              <>
                {selectedImageIndex > 0 && (
                  <button
                    onClick={() => setSelectedImageIndex(selectedImageIndex - 1)}
                    className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-3 rounded-full hover:bg-opacity-75 transition-colors"
                  >
                    <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                    </svg>
                  </button>
                )}

                {selectedImageIndex < images.length - 1 && (
                  <button
                    onClick={() => setSelectedImageIndex(selectedImageIndex + 1)}
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-3 rounded-full hover:bg-opacity-75 transition-colors"
                  >
                    <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </button>
                )}
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

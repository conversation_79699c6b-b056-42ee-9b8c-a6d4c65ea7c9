import Link from "next/link";
import { 
  Repeat, 
  UserPlus, 
  Upload, 
  Search, 
  MessageCircle, 
  Handshake,
  CheckCircle,
  ArrowRight,
  Shield,
  Clock,
  Star
} from "lucide-react";

const steps = [
  {
    number: "1",
    icon: <UserPlus className="h-8 w-8" />,
    title: "أنشئ حسابك",
    description: "سجل في المنصة مجاناً باستخدام بريدك الإلكتروني أو حسابك في Google/Facebook",
    details: [
      "التسجيل مجاني 100%",
      "معلومات أساسية فقط",
      "تفعيل فوري للحساب"
    ]
  },
  {
    number: "2", 
    icon: <Upload className="h-8 w-8" />,
    title: "أضف غرضك",
    description: "ارفع صور غرضك واكتب وصفاً مفصلاً وحدد ما تريده في المقابل",
    details: [
      "حتى 5 صور للغرض الواحد",
      "وصف مفصل وحالة الغرض",
      "تحديد المطلوب في المقابل"
    ]
  },
  {
    number: "3",
    icon: <Search className="h-8 w-8" />,
    title: "ابحث وتصفح",
    description: "تصفح الأغراض المتاحة أو ابحث عن ما تحتاجه باستخدام الفلاتر المتقدمة",
    details: [
      "بحث متقدم بالفئات",
      "فلترة حسب الموقع",
      "ترتيب حسب التاريخ والمسافة"
    ]
  },
  {
    number: "4",
    icon: <MessageCircle className="h-8 w-8" />,
    title: "تواصل وتفاوض",
    description: "تواصل مع صاحب الغرض عبر نظام الرسائل الآمن وناقش تفاصيل التبادل",
    details: [
      "رسائل آمنة داخل المنصة",
      "مشاركة الصور والتفاصيل",
      "تحديد موعد ومكان اللقاء"
    ]
  },
  {
    number: "5",
    icon: <Handshake className="h-8 w-8" />,
    title: "أتمم التبادل",
    description: "التق مع الطرف الآخر في مكان آمن وأتمم عملية التبادل",
    details: [
      "لقاء في مكان عام آمن",
      "فحص الغرض قبل التبادل",
      "تأكيد إتمام العملية"
    ]
  },
  {
    number: "6",
    icon: <Star className="h-8 w-8" />,
    title: "قيّم التجربة",
    description: "قيّم الطرف الآخر وشارك تجربتك لمساعدة المجتمع",
    details: [
      "تقييم من 1 إلى 5 نجوم",
      "كتابة مراجعة اختيارية",
      "بناء سمعة إيجابية"
    ]
  }
];

const tips = [
  {
    icon: <Shield className="h-6 w-6" />,
    title: "نصائح الأمان",
    items: [
      "التق دائماً في مكان عام وآمن",
      "أحضر صديقاً معك إذا أمكن",
      "تأكد من حالة الغرض قبل التبادل",
      "لا تشارك معلومات شخصية حساسة"
    ]
  },
  {
    icon: <CheckCircle className="h-6 w-6" />,
    title: "نصائح للنجاح",
    items: [
      "اكتب وصفاً دقيقاً وصادقاً",
      "ارفع صوراً واضحة وعالية الجودة",
      "كن مرناً في التفاوض",
      "رد على الرسائل بسرعة"
    ]
  },
  {
    icon: <Clock className="h-6 w-6" />,
    title: "توقيت مثالي",
    items: [
      "أفضل أوقات النشر: المساء والعطل",
      "رد على الاستفسارات خلال 24 ساعة",
      "حدد مواعيد مرنة للقاء",
      "كن صبوراً - قد يستغرق الأمر وقتاً"
    ]
  }
];

export default function HowItWorksPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-blue-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-md border-b border-emerald-100 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="flex items-center space-x-2">
              <div className="bg-gradient-to-r from-emerald-500 to-blue-500 p-2 rounded-xl">
                <Repeat className="h-6 w-6 text-white" />
              </div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-emerald-600 to-blue-600 bg-clip-text text-transparent">
                مقايضة
              </h1>
            </Link>
            
            <nav className="hidden md:flex items-center space-x-8">
              <Link href="/browse" className="text-gray-700 hover:text-emerald-600 transition-colors">
                تصفح الأغراض
              </Link>
              <Link href="/how-it-works" className="text-emerald-600 font-semibold">
                كيف يعمل
              </Link>
              <Link href="/about" className="text-gray-700 hover:text-emerald-600 transition-colors">
                من نحن
              </Link>
            </nav>

            <div className="flex items-center space-x-4">
              <Link 
                href="/login" 
                className="text-gray-700 hover:text-emerald-600 transition-colors"
              >
                تسجيل الدخول
              </Link>
              <Link 
                href="/register" 
                className="bg-gradient-to-r from-emerald-500 to-blue-500 text-white px-6 py-2 rounded-full hover:shadow-lg transition-all duration-300 transform hover:scale-105"
              >
                ابدأ المقايضة
              </Link>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Hero Section */}
        <div className="text-center mb-20">
          <h2 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
            <span className="bg-gradient-to-r from-emerald-600 to-blue-600 bg-clip-text text-transparent">
              كيف يعمل الموقع؟
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            المقايضة أسهل مما تتخيل! اتبع هذه الخطوات البسيطة وابدأ رحلتك في عالم التبادل المستدام
          </p>
        </div>

        {/* Steps */}
        <div className="mb-20">
          <div className="grid gap-8">
            {steps.map((step, index) => (
              <div key={index} className="flex flex-col lg:flex-row items-center gap-8">
                {/* Step Number & Icon */}
                <div className="flex-shrink-0">
                  <div className="relative">
                    <div className="w-20 h-20 bg-gradient-to-r from-emerald-500 to-blue-500 rounded-full flex items-center justify-center text-white text-2xl font-bold">
                      {step.number}
                    </div>
                    <div className="absolute -bottom-2 -right-2 bg-white p-2 rounded-full border-4 border-emerald-100">
                      <div className="text-emerald-600">
                        {step.icon}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Content */}
                <div className="flex-1 bg-white/60 backdrop-blur-sm rounded-2xl border border-emerald-100 p-8">
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">{step.title}</h3>
                  <p className="text-gray-700 mb-6 leading-relaxed">{step.description}</p>
                  
                  <div className="grid md:grid-cols-3 gap-4">
                    {step.details.map((detail, detailIndex) => (
                      <div key={detailIndex} className="flex items-center text-sm text-gray-600">
                        <CheckCircle className="h-4 w-4 text-emerald-500 ml-2 flex-shrink-0" />
                        {detail}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Arrow (except for last step) */}
                {index < steps.length - 1 && (
                  <div className="hidden lg:block">
                    <ArrowRight className="h-8 w-8 text-emerald-400" />
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Tips Section */}
        <div className="mb-20">
          <h3 className="text-3xl font-bold text-gray-900 text-center mb-12">نصائح مهمة للنجاح</h3>
          <div className="grid md:grid-cols-3 gap-8">
            {tips.map((tip, index) => (
              <div key={index} className="bg-white/60 backdrop-blur-sm rounded-2xl border border-emerald-100 p-6">
                <div className="flex items-center mb-4">
                  <div className="bg-emerald-100 p-2 rounded-lg text-emerald-600 ml-3">
                    {tip.icon}
                  </div>
                  <h4 className="text-lg font-bold text-gray-900">{tip.title}</h4>
                </div>
                <ul className="space-y-3">
                  {tip.items.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-start text-sm text-gray-700">
                      <div className="w-2 h-2 bg-emerald-500 rounded-full mt-2 ml-3 flex-shrink-0"></div>
                      {item}
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>

        {/* FAQ Quick */}
        <div className="bg-gradient-to-r from-emerald-500 to-blue-500 rounded-2xl p-8 mb-20 text-white">
          <h3 className="text-2xl font-bold text-center mb-8">أسئلة سريعة</h3>
          <div className="grid md:grid-cols-2 gap-8">
            <div>
              <h4 className="font-semibold mb-2">هل الخدمة مجانية؟</h4>
              <p className="text-emerald-100 text-sm">نعم، استخدام المنصة مجاني تماماً ولا نتقاضى أي عمولة.</p>
            </div>
            <div>
              <h4 className="font-semibold mb-2">كم يستغرق العثور على مقايضة؟</h4>
              <p className="text-emerald-100 text-sm">يختلف حسب نوع الغرض، لكن معظم المستخدمين يجدون مقايضة خلال أسبوع.</p>
            </div>
            <div>
              <h4 className="font-semibold mb-2">ماذا لو لم أجد ما أريد؟</h4>
              <p className="text-emerald-100 text-sm">يمكنك إنشاء طلب وسنشعرك عندما يتوفر ما تبحث عنه.</p>
            </div>
            <div>
              <h4 className="font-semibold mb-2">هل يمكنني إلغاء التبادل؟</h4>
              <p className="text-emerald-100 text-sm">نعم، يمكن الإلغاء قبل اللقاء، لكن ننصح بعدم فعل ذلك إلا للضرورة.</p>
            </div>
          </div>
        </div>

        {/* CTA */}
        <div className="text-center">
          <div className="bg-white/60 backdrop-blur-sm rounded-2xl border border-emerald-100 p-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">جاهز للبدء؟</h3>
            <p className="text-gray-600 mb-6">
              انضم لآلاف المستخدمين الذين يتبادلون الأغراض يومياً
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/register"
                className="bg-gradient-to-r from-emerald-500 to-blue-500 text-white px-8 py-3 rounded-xl font-semibold hover:shadow-lg transition-all duration-300 transform hover:scale-105 inline-flex items-center justify-center"
              >
                ابدأ المقايضة الآن
                <ArrowRight className="mr-2 h-5 w-5" />
              </Link>
              <Link
                href="/browse"
                className="border-2 border-emerald-200 text-emerald-600 px-8 py-3 rounded-xl font-semibold hover:bg-emerald-50 transition-colors"
              >
                تصفح الأغراض أولاً
              </Link>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}

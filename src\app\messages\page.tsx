"use client";

import Link from "next/link";
import { useState } from "react";
import { 
  Repeat, 
  Search, 
  Send, 
  Paperclip, 
  MoreVertical,
  User,
  Clock,
  CheckCheck,
  Phone,
  Video,
  Archive,
  Trash2
} from "lucide-react";

// Mock data for conversations
const mockConversations = [
  {
    id: 1,
    user: {
      name: "أحمد محمد",
      avatar: "/api/placeholder/50/50",
      online: true
    },
    item: {
      title: "آيفون 12 مستعمل",
      image: "/api/placeholder/60/60"
    },
    lastMessage: "متى يمكننا اللقاء لإتمام التبادل؟",
    timestamp: "منذ 5 دقائق",
    unread: 2,
    isRead: false
  },
  {
    id: 2,
    user: {
      name: "فاطمة أحمد",
      avatar: "/api/placeholder/50/50",
      online: false
    },
    item: {
      title: "طاولة خشبية كلاسيكية",
      image: "/api/placeholder/60/60"
    },
    lastMessage: "شكراً لك، الطاولة رائعة!",
    timestamp: "منذ ساعة",
    unread: 0,
    isRead: true
  },
  {
    id: 3,
    user: {
      name: "محمد علي",
      avatar: "/api/placeholder/50/50",
      online: true
    },
    item: {
      title: "دراجة هوائية",
      image: "/api/placeholder/60/60"
    },
    lastMessage: "هل يمكنني رؤية صور إضافية؟",
    timestamp: "منذ 3 ساعات",
    unread: 1,
    isRead: false
  },
  {
    id: 4,
    user: {
      name: "سارة خالد",
      avatar: "/api/placeholder/50/50",
      online: false
    },
    item: {
      title: "كتب جامعية",
      image: "/api/placeholder/60/60"
    },
    lastMessage: "تم إتمام التبادل بنجاح",
    timestamp: "أمس",
    unread: 0,
    isRead: true
  }
];

// Mock messages for active conversation
const mockMessages = [
  {
    id: 1,
    sender: "other",
    content: "مرحباً، أنا مهتم بالآيفون الذي نشرته",
    timestamp: "10:30 ص",
    isRead: true
  },
  {
    id: 2,
    sender: "me",
    content: "أهلاً وسهلاً! الجهاز في حالة ممتازة كما ذكرت في الإعلان",
    timestamp: "10:32 ص",
    isRead: true
  },
  {
    id: 3,
    sender: "other",
    content: "رائع، وما الذي تريده في المقابل تحديداً؟",
    timestamp: "10:35 ص",
    isRead: true
  },
  {
    id: 4,
    sender: "me",
    content: "أبحث عن لابتوب أو تابلت بمواصفات جيدة، أو أي جهاز إلكتروني مشابه في القيمة",
    timestamp: "10:37 ص",
    isRead: true
  },
  {
    id: 5,
    sender: "other",
    content: "لدي MacBook Air 2020 في حالة ممتازة، هل تريد صوراً له؟",
    timestamp: "10:40 ص",
    isRead: true
  },
  {
    id: 6,
    sender: "me",
    content: "نعم بالتأكيد! أرسل الصور من فضلك",
    timestamp: "10:42 ص",
    isRead: true
  },
  {
    id: 7,
    sender: "other",
    content: "متى يمكننا اللقاء لإتمام التبادل؟",
    timestamp: "11:15 ص",
    isRead: false
  }
];

export default function MessagesPage() {
  const [selectedConversation, setSelectedConversation] = useState(mockConversations[0]);
  const [newMessage, setNewMessage] = useState("");
  const [searchQuery, setSearchQuery] = useState("");

  const handleSendMessage = () => {
    if (newMessage.trim()) {
      // Here you would typically send the message to your backend
      console.log("Sending message:", newMessage);
      setNewMessage("");
    }
  };

  const filteredConversations = mockConversations.filter(conv =>
    conv.user.name.includes(searchQuery) || 
    conv.item.title.includes(searchQuery)
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-blue-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-md border-b border-emerald-100 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="flex items-center space-x-2">
              <div className="bg-gradient-to-r from-emerald-500 to-blue-500 p-2 rounded-xl">
                <Repeat className="h-6 w-6 text-white" />
              </div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-emerald-600 to-blue-600 bg-clip-text text-transparent">
                مقايضة
              </h1>
            </Link>
            
            <nav className="hidden md:flex items-center space-x-8">
              <Link href="/browse" className="text-gray-700 hover:text-emerald-600 transition-colors">
                تصفح الأغراض
              </Link>
              <Link href="/add-item" className="text-gray-700 hover:text-emerald-600 transition-colors">
                أضف غرض
              </Link>
              <Link href="/messages" className="text-emerald-600 font-semibold">
                الرسائل
              </Link>
            </nav>

            <div className="flex items-center space-x-4">
              <Link 
                href="/profile" 
                className="text-gray-700 hover:text-emerald-600 transition-colors"
              >
                حسابي
              </Link>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white/60 backdrop-blur-sm rounded-2xl border border-emerald-100 overflow-hidden" style={{ height: 'calc(100vh - 200px)' }}>
          <div className="flex h-full">
            {/* Conversations Sidebar */}
            <div className="w-1/3 border-l border-emerald-200 flex flex-col">
              {/* Search */}
              <div className="p-4 border-b border-emerald-200">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <input
                    type="text"
                    placeholder="ابحث في المحادثات..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pr-10 pl-4 py-2 border border-emerald-200 rounded-lg focus:border-emerald-500 focus:outline-none"
                  />
                </div>
              </div>

              {/* Conversations List */}
              <div className="flex-1 overflow-y-auto">
                {filteredConversations.map((conversation) => (
                  <div
                    key={conversation.id}
                    onClick={() => setSelectedConversation(conversation)}
                    className={`p-4 border-b border-emerald-100 cursor-pointer hover:bg-emerald-50 transition-colors ${
                      selectedConversation.id === conversation.id ? 'bg-emerald-50 border-l-4 border-l-emerald-500' : ''
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      {/* User Avatar */}
                      <div className="relative">
                        <div className="w-12 h-12 bg-gradient-to-br from-emerald-100 to-blue-100 rounded-full flex items-center justify-center">
                          <User className="h-6 w-6 text-gray-600" />
                        </div>
                        {conversation.user.online && (
                          <div className="absolute -bottom-1 -left-1 w-4 h-4 bg-green-500 border-2 border-white rounded-full"></div>
                        )}
                      </div>

                      {/* Content */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-1">
                          <h3 className="font-semibold text-gray-900 truncate">{conversation.user.name}</h3>
                          <span className="text-xs text-gray-500">{conversation.timestamp}</span>
                        </div>
                        
                        <div className="flex items-center space-x-2 mb-2">
                          <div className="w-6 h-6 bg-gradient-to-br from-emerald-100 to-blue-100 rounded flex-shrink-0"></div>
                          <span className="text-xs text-gray-600 truncate">{conversation.item.title}</span>
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <p className="text-sm text-gray-600 truncate flex-1">{conversation.lastMessage}</p>
                          {conversation.unread > 0 && (
                            <span className="bg-emerald-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center">
                              {conversation.unread}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Chat Area */}
            <div className="flex-1 flex flex-col">
              {/* Chat Header */}
              <div className="p-4 border-b border-emerald-200 bg-white/80">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="relative">
                      <div className="w-10 h-10 bg-gradient-to-br from-emerald-100 to-blue-100 rounded-full flex items-center justify-center">
                        <User className="h-5 w-5 text-gray-600" />
                      </div>
                      {selectedConversation.user.online && (
                        <div className="absolute -bottom-1 -left-1 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></div>
                      )}
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{selectedConversation.user.name}</h3>
                      <p className="text-sm text-gray-600">
                        {selectedConversation.user.online ? 'متصل الآن' : 'آخر ظهور منذ ساعة'}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <button className="p-2 text-gray-600 hover:text-emerald-600 hover:bg-emerald-50 rounded-lg transition-colors">
                      <Phone className="h-5 w-5" />
                    </button>
                    <button className="p-2 text-gray-600 hover:text-emerald-600 hover:bg-emerald-50 rounded-lg transition-colors">
                      <Video className="h-5 w-5" />
                    </button>
                    <button className="p-2 text-gray-600 hover:text-emerald-600 hover:bg-emerald-50 rounded-lg transition-colors">
                      <MoreVertical className="h-5 w-5" />
                    </button>
                  </div>
                </div>

                {/* Item Info */}
                <div className="mt-3 p-3 bg-emerald-50 rounded-lg flex items-center space-x-3">
                  <div className="w-12 h-12 bg-gradient-to-br from-emerald-100 to-blue-100 rounded-lg flex-shrink-0"></div>
                  <div>
                    <h4 className="font-medium text-gray-900">{selectedConversation.item.title}</h4>
                    <p className="text-sm text-gray-600">الغرض المطلوب للمقايضة</p>
                  </div>
                </div>
              </div>

              {/* Messages */}
              <div className="flex-1 overflow-y-auto p-4 space-y-4">
                {mockMessages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.sender === 'me' ? 'justify-start' : 'justify-end'}`}
                  >
                    <div
                      className={`max-w-xs lg:max-w-md px-4 py-2 rounded-2xl ${
                        message.sender === 'me'
                          ? 'bg-emerald-500 text-white'
                          : 'bg-white border border-emerald-200 text-gray-900'
                      }`}
                    >
                      <p className="text-sm">{message.content}</p>
                      <div className={`flex items-center justify-end mt-1 space-x-1 ${
                        message.sender === 'me' ? 'text-emerald-100' : 'text-gray-500'
                      }`}>
                        <span className="text-xs">{message.timestamp}</span>
                        {message.sender === 'me' && (
                          <CheckCheck className={`h-3 w-3 ${message.isRead ? 'text-emerald-200' : 'text-emerald-300'}`} />
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Message Input */}
              <div className="p-4 border-t border-emerald-200 bg-white/80">
                <div className="flex items-center space-x-3">
                  <button className="p-2 text-gray-600 hover:text-emerald-600 hover:bg-emerald-50 rounded-lg transition-colors">
                    <Paperclip className="h-5 w-5" />
                  </button>
                  
                  <div className="flex-1 relative">
                    <input
                      type="text"
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                      placeholder="اكتب رسالتك..."
                      className="w-full px-4 py-2 border border-emerald-200 rounded-full focus:border-emerald-500 focus:outline-none"
                    />
                  </div>
                  
                  <button
                    onClick={handleSendMessage}
                    disabled={!newMessage.trim()}
                    className="p-2 bg-emerald-500 text-white rounded-full hover:bg-emerald-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <Send className="h-5 w-5" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

# مقايضة - منصة التبادل الذكية

## 🌟 نظرة عامة

**مقايضة** هي منصة عصرية ومبتكرة تتيح للمستخدمين تبادل الأغراض التي لا يحتاجونها مع أشخاص آخرين بدلاً من بيعها أو شرائها. تهدف المنصة إلى بناء مجتمع مستدام وصديق للبيئة من خلال إعادة استخدام الأغراض وتقليل الهدر.

## ✨ المميزات الرئيسية

### 🏠 الصفحة الرئيسية
- تصميم عصري وجذاب مع تدرجات لونية جميلة
- شريط بحث متقدم للعثور على الأغراض المطلوبة
- عرض أحدث العروض والفئات المختلفة
- واجهة مستخدم سهلة ومرنة

### 🔍 تصفح الأغراض
- فلترة متقدمة حسب الفئة والموقع والحالة
- عرض تفاصيل شاملة لكل غرض
- نظام تقييم للمستخدمين
- خيارات بحث ذكية

### ➕ إضافة الأغراض
- رفع صور متعددة للغرض (حتى 5 صور)
- وصف مفصل وتحديد الحالة
- تحديد ما يريده المستخدم في المقابل
- اختيار الموقع الجغرافي

### 👤 إدارة الحسابات
- تسجيل دخول آمن
- إنشاء حساب جديد
- تسجيل دخول عبر Google و Facebook
- ملف شخصي شامل مع التقييمات

### 💬 نظام الرسائل
- محادثة داخلية آمنة بين المستخدمين
- إشعارات فورية
- حفظ تاريخ المحادثات

### 🛡️ الأمان والثقة
- نظام تقييم المستخدمين
- إرشادات السلامة
- إمكانية الإبلاغ عن المستخدمين المشبوهين
- التحقق من الحسابات

## 🛠️ التقنيات المستخدمة

### Frontend
- **Next.js 14** - إطار عمل React متقدم
- **TypeScript** - للكتابة الآمنة والمنظمة
- **Tailwind CSS** - للتصميم السريع والمرن
- **Lucide React** - مكتبة أيقونات عصرية

### التصميم
- **خط Cairo** - خط عربي جميل ومقروء
- **تدرجات لونية** - من الأخضر الزمردي إلى الأزرق
- **Glass Morphism** - تأثيرات زجاجية عصرية
- **Responsive Design** - متجاوب مع جميع الأجهزة

### المميزات التقنية
- **RTL Support** - دعم كامل للغة العربية
- **Dark Mode Ready** - جاهز للوضع الليلي
- **SEO Optimized** - محسن لمحركات البحث
- **Performance** - أداء سريع ومحسن

## 🚀 كيفية التشغيل

### المتطلبات
- Node.js 18+
- npm أو yarn

### خطوات التشغيل

1. **تثبيت المكتبات**
```bash
npm install
```

2. **تشغيل الخادم المحلي**
```bash
npm run dev
```

3. **فتح المتصفح**
```
http://localhost:3000
```

## 📱 الصفحات المتاحة

| الصفحة | الرابط | الوصف |
|--------|--------|--------|
| الرئيسية | `/` | الصفحة الرئيسية مع البحث والمميزات |
| تصفح الأغراض | `/browse` | عرض جميع الأغراض مع الفلترة |
| إضافة غرض | `/add-item` | نموذج إضافة غرض جديد |
| تسجيل الدخول | `/login` | صفحة تسجيل الدخول |
| إنشاء حساب | `/register` | صفحة إنشاء حساب جديد |
| تفاصيل الغرض | `/items/[id]` | عرض تفاصيل غرض محدد |
| الأسئلة الشائعة | `/faq` | إجابات على الأسئلة الشائعة |

## 🎨 التصميم والألوان

### الألوان الرئيسية
- **الأخضر الزمردي**: `#10b981` (emerald-500)
- **الأزرق**: `#3b82f6` (blue-500)
- **الخلفية**: تدرج من `emerald-50` إلى `blue-50`
- **النصوص**: `gray-900` للعناوين، `gray-600` للنصوص العادية

### التأثيرات البصرية
- **Glass Morphism**: خلفيات شفافة مع تأثير الضبابية
- **Hover Effects**: تأثيرات تفاعلية عند التمرير
- **Smooth Transitions**: انتقالات سلسة ومريحة
- **Gradient Backgrounds**: خلفيات متدرجة جميلة

---

**مقايضة** - بدلها بدون فلوس 🔄✨

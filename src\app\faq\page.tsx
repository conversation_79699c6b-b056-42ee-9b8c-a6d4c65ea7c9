"use client";

import Link from "next/link";
import { useState } from "react";
import { ChevronDown, ChevronUp, Repeat, Shield, Users, HelpCircle } from "lucide-react";

const faqs = [
  {
    question: "كيف تعمل منصة مقايضة؟",
    answer: "مقايضة هي منصة تتيح لك تبادل الأغراض التي لا تحتاجها مع أشخاص آخرين. ببساطة، تقوم بنشر غرضك مع تحديد ما تريده في المقابل، ثم يتواصل معك المهتمون لإتمام عملية التبادل."
  },
  {
    question: "هل الخدمة مجانية؟",
    answer: "نعم، استخدام منصة مقايضة مجاني تماماً. لا نتقاضى أي عمولة على عمليات التبادل. هدفنا هو بناء مجتمع مستدام يساعد الناس على الاستفادة من الأغراض بدلاً من إهدارها."
  },
  {
    question: "كيف أضمن صفقة عادلة وآمنة؟",
    answer: "نوفر نظام تقييم للمستخدمين، ونشجع على اللقاء في أماكن عامة آمنة. كما يمكنك مراجعة تقييمات المستخدم الآخر قبل الموافقة على التبادل. ننصح بالتواصل عبر المنصة أولاً للتأكد من جدية الطرف الآخر."
  },
  {
    question: "ماذا لو لم أجد ما أريده؟",
    answer: "يمكنك إنشاء طلب للغرض الذي تريده، وسيتم إشعارك عندما ينشر أحد المستخدمين غرضاً مطابقاً. كما يمكنك تصفح الأغراض المتاحة بانتظام أو استخدام خاصية البحث المتقدم."
  },
  {
    question: "كيف أتواصل مع المستخدمين الآخرين؟",
    answer: "بعد إظهار اهتمامك بغرض معين، ستتمكن من التواصل مع صاحبه عبر نظام الرسائل الداخلي في المنصة. هذا يضمن خصوصيتك ويحفظ سجل المحادثات."
  },
  {
    question: "ما هي شروط نشر الأغراض؟",
    answer: "يجب أن تكون الأغراض في حالة جيدة وقابلة للاستخدام. ممنوع نشر الأغراض المكسورة أو التالفة دون ذكر ذلك بوضوح. كما ممنوع نشر الأغراض المحظورة قانونياً أو الخطيرة."
  },
  {
    question: "كيف أحدد قيمة غرضي مقابل غرض آخر؟",
    answer: "لا توجد قواعد صارمة، فالقيمة نسبية حسب احتياج كل شخص. ننصح بالبحث عن أسعار الأغراض المشابهة في السوق كمرجع، لكن في النهاية الاتفاق يتم بين الطرفين."
  },
  {
    question: "هل يمكنني إلغاء التبادل بعد الاتفاق؟",
    answer: "نعم، يمكن إلغاء التبادل قبل التسليم الفعلي، لكن ننصح بعدم فعل ذلك إلا للضرورة القصوى احتراماً للطرف الآخر. الإلغاء المتكرر قد يؤثر على تقييمك."
  },
  {
    question: "ماذا عن التوصيل؟",
    answer: "حالياً، التوصيل يتم بالاتفاق بين الطرفين. يمكن اللقاء في مكان متفق عليه أو ترتيب التوصيل. نعمل على إضافة خدمة توصيل رسمية قريباً."
  },
  {
    question: "كيف أبلغ عن مستخدم مشبوه؟",
    answer: "يمكنك الإبلاغ عن أي مستخدم مشبوه من خلال زر 'إبلاغ' في صفحة المستخدم أو في المحادثة. فريقنا سيراجع البلاغ ويتخذ الإجراء المناسب."
  }
];

export default function FAQPage() {
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-blue-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-md border-b border-emerald-100 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="flex items-center space-x-2">
              <div className="bg-gradient-to-r from-emerald-500 to-blue-500 p-2 rounded-xl">
                <Repeat className="h-6 w-6 text-white" />
              </div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-emerald-600 to-blue-600 bg-clip-text text-transparent">
                مقايضة
              </h1>
            </Link>
            
            <nav className="hidden md:flex items-center space-x-8">
              <Link href="/browse" className="text-gray-700 hover:text-emerald-600 transition-colors">
                تصفح الأغراض
              </Link>
              <Link href="/how-it-works" className="text-gray-700 hover:text-emerald-600 transition-colors">
                كيف يعمل
              </Link>
              <Link href="/faq" className="text-emerald-600 font-semibold">
                الأسئلة الشائعة
              </Link>
            </nav>

            <div className="flex items-center space-x-4">
              <Link 
                href="/login" 
                className="text-gray-700 hover:text-emerald-600 transition-colors"
              >
                تسجيل الدخول
              </Link>
              <Link 
                href="/register" 
                className="bg-gradient-to-r from-emerald-500 to-blue-500 text-white px-6 py-2 rounded-full hover:shadow-lg transition-all duration-300 transform hover:scale-105"
              >
                ابدأ المقايضة
              </Link>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <div className="bg-gradient-to-r from-emerald-100 to-blue-100 p-4 rounded-full w-20 h-20 mx-auto mb-6 flex items-center justify-center">
            <HelpCircle className="h-10 w-10 text-emerald-600" />
          </div>
          
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            الأسئلة الشائعة
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            إجابات على أكثر الأسئلة شيوعاً حول منصة مقايضة وكيفية استخدامها
          </p>
        </div>

        {/* Quick Stats */}
        <div className="grid md:grid-cols-3 gap-6 mb-16">
          <div className="bg-white/60 backdrop-blur-sm p-6 rounded-2xl border border-emerald-100 text-center">
            <div className="bg-emerald-100 p-3 rounded-xl w-fit mx-auto mb-4">
              <Shield className="h-8 w-8 text-emerald-600" />
            </div>
            <h3 className="text-lg font-bold text-gray-900 mb-2">آمن وموثوق</h3>
            <p className="text-gray-600 text-sm">نظام حماية متقدم للمستخدمين</p>
          </div>

          <div className="bg-white/60 backdrop-blur-sm p-6 rounded-2xl border border-blue-100 text-center">
            <div className="bg-blue-100 p-3 rounded-xl w-fit mx-auto mb-4">
              <Users className="h-8 w-8 text-blue-600" />
            </div>
            <h3 className="text-lg font-bold text-gray-900 mb-2">مجتمع نشط</h3>
            <p className="text-gray-600 text-sm">آلاف المستخدمين النشطين</p>
          </div>

          <div className="bg-white/60 backdrop-blur-sm p-6 rounded-2xl border border-emerald-100 text-center">
            <div className="bg-emerald-100 p-3 rounded-xl w-fit mx-auto mb-4">
              <Repeat className="h-8 w-8 text-emerald-600" />
            </div>
            <h3 className="text-lg font-bold text-gray-900 mb-2">مجاني تماماً</h3>
            <p className="text-gray-600 text-sm">لا توجد رسوم أو عمولات</p>
          </div>
        </div>

        {/* FAQ Section */}
        <div className="bg-white/60 backdrop-blur-sm rounded-2xl border border-emerald-100 p-8">
          <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">
            الأسئلة والأجوبة
          </h3>
          
          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <div
                key={index}
                className="border border-emerald-200 rounded-xl overflow-hidden"
              >
                <button
                  onClick={() => toggleFAQ(index)}
                  className="w-full px-6 py-4 text-right bg-emerald-50/50 hover:bg-emerald-50 transition-colors flex items-center justify-between"
                >
                  <span className="font-semibold text-gray-900">{faq.question}</span>
                  {openIndex === index ? (
                    <ChevronUp className="h-5 w-5 text-emerald-600 flex-shrink-0" />
                  ) : (
                    <ChevronDown className="h-5 w-5 text-emerald-600 flex-shrink-0" />
                  )}
                </button>
                
                {openIndex === index && (
                  <div className="px-6 py-4 bg-white border-t border-emerald-200">
                    <p className="text-gray-700 leading-relaxed">{faq.answer}</p>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Contact Section */}
        <div className="mt-16 text-center">
          <div className="bg-gradient-to-r from-emerald-500 to-blue-500 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">لم تجد إجابة لسؤالك؟</h3>
            <p className="mb-6 opacity-90">
              تواصل معنا وسنكون سعداء لمساعدتك
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/contact"
                className="bg-white text-emerald-600 px-6 py-3 rounded-xl font-semibold hover:shadow-lg transition-all duration-300 transform hover:scale-105"
              >
                تواصل معنا
              </Link>
              <Link
                href="/browse"
                className="border-2 border-white text-white px-6 py-3 rounded-xl font-semibold hover:bg-white hover:text-emerald-600 transition-all duration-300"
              >
                ابدأ المقايضة الآن
              </Link>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
